import { download, get, post } from '../request';
import {
  IMeetingSignInFilter,
  IMeetingSignInRules,
  IPageResponse,
  Result,
  IMeetingSignInDetails,
  IAgendaFilter,
  IMeetingAgenda,
  ISignInPersonFilter,
  ISignInPerson
} from '@haierbusiness-front/common-libs';

export const meetingSignInApi = {
  //签到明细
  list: (params: IMeetingSignInFilter): Promise<IPageResponse<IMeetingSignInDetails>> => {
    return get('/mice-support/api/meeting/check/in/detail/list', params);
  },
  //签到规则
  details: (miceInfoId: number): Promise<IMeetingSignInRules> => {
    return get('/mice-support/api/meeting/check/in/detail', {
      miceInfoId,
    });
  },
  //新增签到规则
  save: (params: IMeetingSignInRules): Promise<Result> => {
    return post('/mice-support/api/meeting/check/in/add', params);
  },

  //导出签到明细
  export: (params: { miceInfoId: number; miceInfoName?: string }): Promise<void> => {
    return download('/mice-support/api/meeting/check/in/exportSignDetail', params)
  },

  //编辑签到规则
  edit: (params: IMeetingSignInRules): Promise<Result> => {
    return post('/mice-support/api/meeting/check/in/update', params);
  },
  //发送签到提醒
  sendNotice: (params: IMeetingSignInRules): Promise<Result> => {
    return post('/mice-support/api/meeting/check/in/sendCheckNotice', params);
  },

  //补签接口
  backCheck: (params: IMeetingSignInRules): Promise<Result> => {
    return post('/mice-support/api/meeting/check/in/back-check', params);
  },

  // 查询已签到的参会人
  queryCheck: (params: ISignInPersonFilter): Promise<IPageResponse<ISignInPerson>> => {
    return get('/mice-support/api/meeting/participant/query-check', params);
  },
};

<script lang="ts" setup>
import {
  message,
  Modal,
  Tag,
} from 'ant-design-vue';
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, h, nextTick, onMounted, ref } from 'vue';
import { usePagination } from 'vue-request';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { meetingAttendeeApi, userApi } from '@haierbusiness-front/apis';
import { attendeeMiceStatusConstant, IMeetingAttendee, IMeetingDetails, IUserListRequest } from '@haierbusiness-front/common-libs';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import type { MenuInfo, MenuItemType } from 'ant-design-vue/lib/menu/src/interface'

import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
//路由参数
const id = Number(route.query.id)
//导入模板地址
const dynamicUrl = new URL('../import/attendeeMeeting.xlsx', import.meta.url).href;
//新增/编辑弹框可见
const Visible = ref(false)
//新增/编辑弹框标题
const title = ref('')
//导入弹窗
const importModel = ref(false)
//表单ref
const formRef = ref();
//是否禁用编辑
const isEdit = ref(false)
//导入文件
let ConferenceDocuments = ref<File | null>();
//选择文件按钮
const fileInputRef = ref<HTMLInputElement | null>(null);
//表单内容-参会人
const attendeeMeeting = ref<IMeetingAttendee>({})
//不同审批状态更新列表数据
const approvalStatus = ref<number | null>()
const uploadLoading = ref<boolean>(false);
const confirmLoading = ref<boolean>(false);

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(meetingAttendeeApi.list);
//表格数据
const dataSource = computed(() => data.value?.list.records || []);

//编辑参会人
const handleEdit = async (record: any) => {
  nextTick(() => {
    formRef.value?.clearValidate()
  })
  title.value = '编辑参会人'
  await attendeeDetail(record.id)
  Visible.value = true
  isEdit.value = true
}
//新增参会人
const handleAdd = () => {
  title.value = '新增参会人'
  attendeeMeeting.value = {}
  nextTick(() => {
    formRef.value?.resetFields()
  })
  Visible.value = true
  isEdit.value = false
}
//导入弹框
const handleImport = () => {
  ConferenceDocuments.value = null
  importModel.value = true
}
//导出文件
const handleExport = async () => {
  if (!meetingDetails.value?.miceName) {
    message.error('会议信息获取失败，请刷新页面重试')
    return
  }

  uploadLoading.value = true
  try {
    const params = {
      miceInfoId: Number(id) || 0,
      miceInfoName: meetingDetails.value.miceName
    }
    await meetingAttendeeApi.export(params)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    uploadLoading.value = false
  }
}

// 触发文件选择
const triggerFileSelect = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

// 选择文件
const handleFileChange = (e: Event) => {
  const input = e.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    ConferenceDocuments.value = input.files[0];
  }
};
//导入确定
const handleFileImport = () => {
  if (!ConferenceDocuments.value) {
    message.error('请先选择文件');
    return;
  }

  uploadLoading.value = true;

  const formData = new FormData();
  formData.append("file", ConferenceDocuments.value);
  formData.append("miceInfoId", '1');

  meetingAttendeeApi.import(formData as any)
    .then(res => {
      message.success('导入成功');
      importModel.value = false;
      listApiRun({
        miceInfoId: id,
        pageNum: 1,
        pageSize: 50
      })
    })
    .catch((error) => {
      console.error('导入失败:', error);
      message.error('导入失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
}


//会议详情
const meetingDetails = ref<IMeetingDetails>()
//标签列表
const groupLists = ref()

const meetingDetail = async () => {
  const meetingId = Number(id)
  if (!meetingId) {
    message.error('会议ID缺失')
    return
  }

  try {
    const response = await meetingAttendeeApi.details(meetingId)
    if (response) {
      meetingDetails.value = response
    }
  } catch (error) {
    console.error('获取参会人详情失败:', error)
    message.error('获取参会人详情失败')
  }
}
//参会人详情
const attendeeDetail = async (id: number) => {
  try {
    const response = await meetingAttendeeApi.attendeeDetails(id)
    if (response) {
      attendeeMeeting.value = response
      console.log(attendeeMeeting.value, "attendeeMeeting.value");
    }
    let tagList: string | string[] | undefined = []
    if (attendeeMeeting.value.tagName) {
      tagList = (attendeeMeeting.value.tagName as string).split(',')
      attendeeMeeting.value.tagName = tagList
    } else {
      attendeeMeeting.value.tagName = tagList
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    message.error('获取会议详情失败')
  }
}
// 获取标签列表
const getGroupList = async (id: number) => {
  const params = {
    miceInfoId: id
  }
  try {
    const response = await meetingAttendeeApi.groupList(params)
    if (response) {
      groupLists.value = response
    }
  } catch (error) {
    console.error('获取标签列表失败:', error)
    message.error('获取标签列表失败')
  }
}
//相关状态数量
const stateNum = ref()
onMounted(async () => {
  const meetingId = Number(id)
  const response = await meetingAttendeeApi.list({
    miceInfoId: meetingId,
    pageNum: 1,
    pageSize: 50
  })
  stateNum.value = response
  await meetingDetail()
  handleShow(1)
  await getGroupList(meetingId)
  
})
//表格内容字段
const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '80px',
    // sorter: (a, b) => a.index - b.index,
    align: 'center',
    customRender: ({ index }) => index + 1
  },
  {
    title: '参会人企业',
    dataIndex: 'enterpriseName',
    width: '280px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '姓名',
    dataIndex: 'nickName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '工号',
    dataIndex: 'userName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '需要住宿',
    dataIndex: 'isStay',
    // sorter: true,
    width: '120px',
    ellipsis: true,
    align: 'center',
    customRender: ({ text }) => text === true ? '是' : '否',
    customCell: (record) => {
      return {
        style: { color: record.isStay === true ? '#52C41A' : '#FF5533' }
      }
    }
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: '160px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '标签',
    dataIndex: 'tagName',
    width: '160px',
    align: 'center',
    customRender: ({ text }) => {
      if (!text) {
        return
      }   // 处理null/undefined情况
      const textList = text ? text.toString().split(',').filter(Boolean) : [] // 确保字符串操作
      return h('div',
        textList.map((tag: string) =>
          h(Tag, {
            key: tag,
            color: tag == '会务组人员' ? 'green' : tag == '接待人员' ? 'geekblue' : tag == '普通参会人' ? '#ccc' : 'orange'
          }, () => tag)
        )
      )
    }

  },
  {
    title: '所属单位',
    dataIndex: 'companyName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '所属部门',
    dataIndex: 'departName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '禁忌与偏好',
    dataIndex: 'specialRequest',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '需座位牌',
    dataIndex: 'isSeatSign',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text === true ? '是' : '否',
    customCell: (record) => {
      return {
        style: { color: record.isSeatSign === true ? '#52C41A' : '#FF5533' }
      }
    }
  },
  {
    title: '需胸牌',
    dataIndex: 'isBreastPiece',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text === true ? '是' : '否',
    customCell: (record) => {
      return {
        style: { color: record.isBreastPiece === true ? '#52C41A' : '#FF5533' }
      }
    }
  },
  {
    title: '参会人来源',
    dataIndex: 'userSource',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text == 1 ? '录入' : text == 2 ? '自主报名' : ' '
  },
  {
    title: '报名审批状态',
    dataIndex: 'approveState',
    width: '120px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      return attendeeMiceStatusConstant.ofType(text)?.desc

    }
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '220px',
    fixed: 'right',
    align: 'center',
  },

];
//分页
const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.list.total,
  current: data.value?.list.pageNum,
  pageSize: data.value?.list.pageSize,
  style: { justifyContent: 'right' },
}));
//表格变化
const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  loading.value = true
  const meetingId = Number(id)
  if (!meetingId) return
  try {
    listApiRun({
      approveState:approvalStatus.value,
      miceInfoId: meetingId,
      pageNum: pag.current,
      pageSize: pag.pageSize
    })
    loading.value = false
  } catch (error) {

  } finally {
    loading.value = false
  }
};
//表单校验
const rules = {
  enterpriseName: [
    { required: true, message: '请选择', trigger: 'change' }
  ],
  nickName: [
    { required: true, message: '请输入', trigger: 'blur' }
  ],
  userName: [
    { required: true, message: '请输入', trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: '请输入', trigger: 'blur' },
  ],
  isSeatSign: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
  isBreastPiece: [
    { required: true, message: '请选择', trigger: 'change' },
  ],
};
//新增或编辑确认处理
const handleOk = async () => {
  confirmLoading.value = true;
  try {
    await formRef.value.validate();
    
    const params = {
      ...attendeeMeeting.value,
      miceInfoId: Number(id),
      tagName: normalizeTagNames(attendeeMeeting.value.tagName)
    };

    const apiMethod = title.value === '新增参会人' 
      ? meetingAttendeeApi.save 
      : meetingAttendeeApi.edit;

    await apiMethod(params);
    message.success(`${title.value}成功`);
    await refreshList();
    
  } catch (error) {
    message.error(`${title.value}失败`);
  } finally {
    confirmLoading.value = false;
    Visible.value = false;
  }
};
//标签转为数组
const normalizeTagNames = (tagNameList:string | string[] | undefined) => {
  if (!tagNameList) return undefined;
  return typeof tagNameList === 'string' 
    ? tagNameList.trim() 
    : tagNameList.filter(Boolean).join(',');
};

//刷新列表
const refreshList = () => listApiRun({
  miceInfoId: Number(id),
  pageNum: 1,
  pageSize: 50
});

//选择标签变化-触发存储标签
const handleChange = (value: string | null | undefined) => {
  if (!value) return false;
  // 转换为数组并过滤空值/空白字符串
  const valueList = value.toString()
    .split(',')
    .map(item => item.trim())
    .filter(Boolean);

  console.log(valueList, "filtered valueList");

  valueList.forEach((item) => {
    const result = groupLists.value.find((tag: { tagName: string }) =>
      tag.tagName === item.trim()
    );
    if (!result) {
      groupAdd(item);
    }
  });
};

//新增标签
const groupAdd = async (tag: string) => {
  const meetingId = Number(id)
  const params = [{
    miceInfoId: id,
    tagName: tag
  }]
  try {
    const response = await meetingAttendeeApi.groupSave(params)
    getGroupList(meetingId)
  } catch (error) {
    console.error('新增标签失败:', error)
    message.error('新增标签失败')
  }
}

//审批通过
const Approved = (record: IMeetingAttendee) => {
  loading.value = true
  const params = {
    id: record.id,
    approveState: 20
  }
  meetingAttendeeApi.Approval(params)
    .then(() => {
      message.success('审批通过成功')
    })
    .catch(() => {
      message.success('审批通过失败')
    })
  listApiRun({
    miceInfoId: id,
    pageNum: 1,
    pageSize: 50
  })
  loading.value = false

}
//审批驳回
const approvalRejection = (record: IMeetingAttendee) => {
  loading.value = true
  const params = {
    id: record.id,
    approveState: 30
  }
  meetingAttendeeApi.Approval(params)
    .then(() => {
      message.success('驳回成功')
    })
    .catch(() => {
      message.success('驳回失败')
    })
  listApiRun({
    miceInfoId: id,
    pageNum: 1,
    pageSize: 50
  })
  loading.value = false
}

// 更多按钮数组
const computedOptions = (state: number, record: IMeetingAttendee) => {
  let options: MenuItemType[] = []
  if (state == 10) {
    options.push(
      {
        key: '1',
        label: '审核通过',
      },
      {
        key: '2',
        label: '审核驳回',
      }
    )
  }
  return options
}

// 点击数组中的菜单项
const handleMenuClick = (record: IMeetingAttendee, e: MenuInfo) => {
  if (e.key === '1') {
    Approved(record)
  }
  if (e.key === '2') {
    approvalRejection(record)
  }
}

//删除
const handleDelete = (id: number) => {
  Modal.confirm({
    title: '删除提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', { style: 'color:red;' }, '确定要删除'),
    onOk() {
      meetingAttendeeApi.remove(id)
        .then(() => {
          message.success('删除成功')
          listApiRun({
            miceInfoId: 1,
            pageNum: 1,
            pageSize: 50
          })
        })
    },
    onCancel() {
      console.log('Cancel');
    },
  });

}

const handleName = (res: string | undefined) => {
  nextTick(() => {
    formRef.value?.clearValidate()
  })
  if (res) {
    if (res == '海尔') {
      isEdit.value = true
      formRef.value?.clearValidate('nickName', 'phone', 'idCard', 'companyName', 'departName')
    } else if (res == '非海尔') {
      isEdit.value = false
      if (!isEdit.value) {
        message.warning('您不是海尔员工，请自行填写信息')
        return
      }
    }
  }
}

// 查询用户-反显用户数据
const getUserList = async (username: string | undefined,) => {
  if (!attendeeMeeting.value.enterpriseName && username) {
    message.warning('请先选择所属企业')
    return
  }
  if (!isEdit.value) {
    return
  }

  if (!username) return
  const params = {
    pageNum: 1,
    pageSize: 20,
    username,
    enterpriseCode: 'haier'
  }
  try {
    const data = await userApi.list(params as IUserListRequest)
    console.log(data, "data");
    nextTick(() => {
      if (data.records?.length > 0) {
        attendeeMeeting.value.phone = data.records[0].phone
        attendeeMeeting.value.userName = data.records[0].username
        attendeeMeeting.value.nickName = data.records[0].nickName
        attendeeMeeting.value.departName = data.records[0].departmentName
        attendeeMeeting.value.companyName = data.records[0].enterpriseName
        attendeeMeeting.value.enterpriseCode = data.records[0].enterpriseCode
      } else {
        message.warning('工号错误，请重新输入')
        formRef.value?.clearValidate('nickName', 'phone', 'idCard', 'companyName', 'departName')
      }
    })

  } catch (error) {
    console.error('查询用户失败:', error)
  }
}

// 复制文本到剪贴板
const getCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功！');
  } catch (err) {
    message.error('复制失败');
  }
};
//按钮显示样式
const btnShow = ref(0)
//不同状态获取列表数据-简单处理-approveState
const handleShow = (res: number) => {
  const meetingId = Number(id)
  switch (res) {
    case 1:
      //审批通过
      listApiRun({
        miceInfoId: meetingId,
        pageNum: 1,
        pageSize: 50,
        approveState: 20
      })
      btnShow.value = res
      approvalStatus.value = 20
      break;
    case 2:
      //审核驳回
      listApiRun({
        miceInfoId: meetingId,
        pageNum: 1,
        pageSize: 50,
        approveState: 30
      })
      btnShow.value = res
      approvalStatus.value = 30
      break;
    case 3:
      //待审核
      listApiRun({
        miceInfoId: meetingId,
        pageNum: 1,
        pageSize: 50,
        approveState: 10
      })
      btnShow.value = res
      approvalStatus.value = 10
      break;
    case 4:
      //全部
      listApiRun({
        miceInfoId: meetingId,
        pageNum: 1,
        pageSize: 50
      })
      approvalStatus.value = null
      btnShow.value = res
      break;
  }
}
//下载导入模板
const downloadStaticFile = () => {
  const link = document.createElement('a');
  link.href = dynamicUrl; // 静态文件路径
  link.download = '参会人模板.xlsx'; // 自定义文件名
  link.click();
};
</script>

<template>
  <div class="container">
    <div class="content">
      <div class="main">
        <div class="main-top">
          <div class="top-title">
            {{ router.currentRoute?.value.meta.title }}
          </div>
          <ul class="top-right">
            <li class="" style="margin-right: 10px;">
              <a-button type="primary" @click="handleAdd">添加</a-button>
            </li>
            <li>
              <a-button @click="handleImport" style="margin-right: 10px;">导入</a-button>
            </li>
            <li>
              <a-button @click="handleExport" style="margin-right: 10px;" :loading="uploadLoading">导出</a-button>
            </li>
            <li class="copy" style="margin-left: 10px;cursor: pointer;" v-if="meetingDetails?.signUpUrl"
              @click="getCopy(meetingDetails?.signUpUrl)"><img src="@/assets/image/meeting/copy.png" alt="">复制报名链接</li>
            <li v-else>
              <a-button @click="">生成报名链接</a-button>
            </li>
          </ul>
        </div>
        <div class="center-main">
          <div class="center-top">
            <a-button :type="btnShow == 1? 'primary' : 'default'" style="margin-right: 10px;border-radius: 4px;" @click="handleShow(1)">审核通过<span>{{
              stateNum?.approvePassCount
                }}</span></a-button>
            <a-button :type="btnShow == 2? 'primary' : 'default'" style="margin-right: 10px;border-radius: 4px;" @click="handleShow(2)">审核驳回<span>{{
              stateNum?.approveRejectCount
                }}</span></a-button>
            <a-button :type="btnShow == 3? 'primary' : 'default'" type="default" style="margin-right: 10px;border-radius: 4px;" @click="handleShow(3)">待审核<span>{{
              stateNum?.approveWaitCount
                }}</span></a-button>
            <a-button :type="btnShow == 4? 'primary' : 'default'" type="default" style="margin-right: 10px;border-radius: 4px;" @click="handleShow(4)">全部<span>{{
              stateNum?.list.total
                }}</span></a-button>
          </div>
          <div class="center-middle">
            <a-table :rowKey="(record: any) => record.id" :columns="columns" :data-source="dataSource"
              :loading="loading" :pagination="pagination" @change="handleTableChange($event as any)"
              :scroll="{ x: 1280 }">
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === '_operator'">
                  <div>
                    <a-button type="link" @click="handleEdit(record)">编辑</a-button>
                    <a-button type="link" @click="handleDelete(record.id)">删除</a-button>
                    <Actions :menu-options="computedOptions(record.approveState, record)"
                      :on-menu-click="(key) => handleMenuClick(record, key)"></Actions>
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
    <Modal v-model:open="Visible" :title="title" width="800px" :confirm-loading="confirmLoading" @ok="handleOk">
      <a-form ref="formRef" :rules="rules" :model="attendeeMeeting" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }"
        :hideRequiredMark="true">
        <a-form-item label="参会人企业：" name="enterpriseName">
          <a-select id="isStay" v-model:value="attendeeMeeting.enterpriseName" class="full-width" allow-clear
            placeholder="请选择参会人企业" @change="handleName(attendeeMeeting.enterpriseName)">
            <a-select-option :value="'海尔'">海尔</a-select-option>
            <a-select-option :value="'非海尔'">非海尔</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="工号：" name="userName">
          <a-input v-model:value="attendeeMeeting.userName" placeholder="请输入工号" allow-clear
            @change="getUserList(attendeeMeeting.userName)" />
        </a-form-item>
        <a-form-item label="姓名：" name="nickName">
          <a-input v-model:value="attendeeMeeting.nickName" placeholder="请输入姓名" allow-clear :disabled="isEdit" />
        </a-form-item>
        <a-form-item label="手机号：" name="phone">
          <a-input v-model:value="attendeeMeeting.phone" placeholder="请输入手机号" :disabled="isEdit" allow-clear />
        </a-form-item>

        <a-form-item label="需要住宿：" name="isStay">
          <a-select id="isStay" v-model:value="attendeeMeeting.isStay" class="full-width" allow-clear
            placeholder="请选择是否住宿">
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="身份证号：" name="idCard">
          <a-input v-model:value="attendeeMeeting.idCard" placeholder="请输入身份证号" allow-clear />
        </a-form-item>

        <a-form-item label="标签：" name="tagName">
          <a-select id="isStay" mode="tags" v-model:value="attendeeMeeting.tagName" class="full-width" allow-clear
            @change="handleChange" placeholder="请选择标签">
            <a-select-option v-for="item in groupLists" :key="item.id" :value="item.tagName">
              {{ item.tagName }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="所属单位：" name="companyName">
          <a-input v-model:value="attendeeMeeting.companyName" placeholder="请输入所属单位" :disabled="isEdit" allow-clear />
        </a-form-item>

        <a-form-item label="所属部门：" name="departName">
          <a-input v-model:value="attendeeMeeting.departName" placeholder="请输入所属部门" :disabled="isEdit" allow-clear />
        </a-form-item>

        <a-form-item label="禁忌与偏好：" name="specialRequest">
          <a-input v-model:value="attendeeMeeting.specialRequest" placeholder="请输入禁忌与偏好" allow-clear />
        </a-form-item>

        <a-form-item label="备注：" name="remark">
          <a-textarea v-model:value="attendeeMeeting.remark" :rows="4" :maxlength="500" show-count placeholder="请输入备注"
            allow-clear />
        </a-form-item>

        <a-form-item label="需座位牌：" name="isSeatSign">
          <a-select id="tagName" v-model:value="attendeeMeeting.isSeatSign" class="full-width" allow-clear
            placeholder="请选择是否需要">
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="需胸牌：" name="isBreastPiece">
          <a-select id="tagName" v-model:value="attendeeMeeting.isBreastPiece" class="full-width" allow-clear
            placeholder="请选择是否需要">
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </Modal>

    <Modal v-model:open="importModel" title="导入参会人" width="35%" :confirm-loading="confirmLoading">
      <template #footer>
        <a-button key="back" @click="importModel = false">取消</a-button>
        <a-button key="submit" type="primary" @click="handleFileImport">导入</a-button>
      </template>
      <a-row :gutter="[16, 16]" style="margin-top: 20px;">
        <a-col :span="6" style="text-align: right;line-height: 32px;">
          上传文件：
        </a-col>
        <a-col :span="10">
          <input ref="fileInputRef" type="file" style="display: none;" accept=".xlsx,.xls,.csv"
            @change="handleFileChange" />
          <a-button type="primary" @click="triggerFileSelect" style="margin-right: 8px;">
            <UploadOutlined /> 选择文件
          </a-button>
        </a-col>
        <a-col :span="8" style="line-height: 32px;">
          <a-button type="link" @click="downloadStaticFile()">下载导入模板</a-button>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="15" style="line-height: 32px;text-align: right;margin-top: 0px;">
          <span v-if="ConferenceDocuments">{{ ConferenceDocuments.name }}</span>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24" style="line-height: 32px;text-align: center;margin-top: 20px;">
          <img src="../../assets/image/orderList/warn.png" alt=""
            style="width: 17px;height: 17px;vertical-align: middle;">导入新数据后将覆盖<span
            style="color: red;">来源为录入</span>的全部数据
        </a-col>

      </a-row>
    </Modal>
  </div>
</template>
<style lang="scss" scoped>
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

:deep(.ant-select) {
  min-width: 100px;
}

.container {
  min-height: 100vh;
  width: 100%;
  padding-bottom: 20px;
  background: #F6F7F9;
}

.content {
  width: 1280px;
  height: auto;
  margin: 0 auto;
  padding-top: 54px;

  .main {
    width: 100%;
    background-color: #fff;
    border-radius: 12px;
    .main-top {
      width: 100%;
      border-bottom: 1px solid #E5E6EB;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 18px 24px;
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;

      .top-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #1D2129;
        line-height: 28px;
        text-align: left;
        font-style: normal;
      }

      .top-right {
        display: flex;
        align-items: center;
        margin-bottom: 0 !important;

        .copy {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #1868DB;
          line-height: 20px;
          text-align: right;
          font-style: normal;

          img {
            width: 16px;
            height: 16px;
          }
        }
      }

    }

    .center-main {
      padding: 20px 24px;

      .center-top {
        display: flex;

        span {
          margin-left: 2px;
        }
      }

      .top-left {
        width: 109px;
        height: 32px;
        background: #1868DB;
        border-radius: 4px;
        line-height: 32px;
        text-align: center;
        font-size: 14px;
        color: #fff;
        margin-right: 12px;
      }

      .top-center {
        width: 185px;
        height: 32px;
        background: #F2F3F5;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        color: #1D2129;
        line-height: 22px;
        text-align: center;
        font-style: normal;

        .line {
          width: 1px;
          height: 18px;
          background: #E7E7E7;
        }
      }

      .center-middle {
        margin-top: 20px;
      }
    }
  }
}

.nav {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909C;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 16px 0px;
}

.full-width {
  width: 100%;
}

.hide-empty-tags .ant-tag {
  display: none;
}

/* 覆盖浏览器自动填充样式 */
:deep(input:-internal-autofill-previewed,
  input:-internal-autofill-selected) {
  -webkit-text-fill-color: #2a2d33;
  /* 保持文字颜色 */
  background-color: #fff !important;
  transition: background-color 5000s ease-out 0.5s;
  /* 延迟背景色渲染 */
}
</style>
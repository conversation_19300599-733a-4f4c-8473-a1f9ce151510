<!-- 平台服务费缴纳 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  Modal,
  message,
  TableProps,
  Table as ATable,
  Tabs as ATabs,
  TabPane as ATabPane,
  Textarea as ATextarea,
  DatePicker as hDatePicker,
  InputNumber as hInputNumber,
  Tooltip,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { paymentFromApi, fileApi } from '@haierbusiness-front/apis';
import { IPaymentFromFilter, IPaymentFrom } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog } from '@haierbusiness-front/composables';
import router from '../../router';
import { PaymentFromStatusEnum, PaymentFromStatusMap } from '@haierbusiness-front/common-libs';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
import { flatMap } from 'lodash';
// const router = useRouter()

const currentRouter = ref();

onMounted(async () => {
  currentRouter.value = await router;
  // 页面初始化时调用列表接口
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  });
});

const columns: ColumnType[] = [
  {
    title: '缴费单号',
    dataIndex: 'receivePaymentCode',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '结算比例',
    dataIndex: 'settlementRate',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '收款金额',
    dataIndex: 'receivePaymentAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<IPaymentFromFilter>({});
const { data, run: listApiRun, loading, current, pageSize } = usePagination(paymentFromApi.getMerchantPaymentPage);

const reset = () => {
  searchParam.value = {};
  beginAndEnd.value = undefined;
};

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  class: 'pagination-center',
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

// View变量已删除，功能合并到viewMode中
const detailVisible = ref(false);
const currentDetailRecord = ref<any>(null);
const detailLoading = ref(false);
const viewMode = ref<'view' | 'upload'>('view'); // 弹窗模式：查看或上传

// 上传发票相关
const invoiceModalVisible = ref(false);
const invoiceLoading = ref(false);
const currentInvoiceRecord = ref<any>(null);
const invoiceForm = ref({
  invoiceType: 1, // 默认选择国旅
  invoiceDate: '',
  invoiceNumber: '',
  invoiceAmount: undefined,
});

//查看
const handleView = (record?: any) => {
  if (record && record.id) {
    // 有传入记录，获取详情数据
    detailLoading.value = true;
    viewMode.value = 'view';
    paymentFromApi
      .getMerchantPaymentDetails(record.id)
      .then((res) => {
        currentDetailRecord.value = res;
        detailVisible.value = true;
      })
      .catch((error) => {
        console.error('获取详情失败:', error);
      })
      .finally(() => {
        detailLoading.value = false;
      });
  } else {
    // 关闭详情弹窗
    detailVisible.value = false;
    currentDetailRecord.value = null;
    fileList.value = [];
    ReasonsRejection.value = '';
  }
};

const beginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => beginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
    } else {
      searchParam.value.startTime = undefined;
      searchParam.value.endTime = undefined;
    }
  },
);

// 上传付款凭证相关
const uploadLoading = ref(false);
const fileList = ref<any[]>([]);
const ReasonsRejection = ref('');
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 状态颜色映射
const getStatusColor = (status: number) => {
  switch (status) {
    case PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD:
      return 'orange';
    case PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM:
      return 'blue';
    case PaymentFromStatusEnum.PAYMENT_REJECTED:
      return 'red';
    case PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD:
      return 'cyan';
    case PaymentFromStatusEnum.APPROVAL_REJECTED:
      return 'volcano';
    case PaymentFromStatusEnum.APPROVING:
      return 'gold';
    case PaymentFromStatusEnum.COMPLETED:
      return 'green';
    default:
      return 'default';
  }
};

// 打开上传付款凭证弹窗
const openUploadModal = (record: any) => {
  detailLoading.value = true;
  viewMode.value = 'upload';
  // 获取详情数据
  paymentFromApi
    .getMerchantPaymentDetails(record.id)
    .then((res) => {
      currentDetailRecord.value = res;
      detailVisible.value = true;
      fileList.value = [];
      ReasonsRejection.value = '';
    })
    .catch((error) => {
      console.error('获取详情失败:', error);
    })
    .finally(() => {
      detailLoading.value = false;
    });
};

// 关闭上传弹窗（已合并到handleView函数中）

// 打开上传发票弹窗
const openInvoiceModal = (record: any) => {
  currentInvoiceRecord.value = record;
  invoiceModalVisible.value = true;
  // 重置表单
  invoiceForm.value = {
    invoiceType: 1,
    invoiceDate: '',
    invoiceNumber: '',
    invoiceAmount: undefined,
  };
};

// 关闭上传发票弹窗
const closeInvoiceModal = () => {
  invoiceModalVisible.value = false;
  currentInvoiceRecord.value = null;
  invoiceForm.value = {
    invoiceType: 1,
    invoiceDate: '',
    invoiceNumber: '',
    invoiceAmount: undefined,
  };
};

// 提交上传发票
const submitInvoice = () => {
  // 验证表单
  if (!invoiceForm.value.invoiceDate) {
    message.error('请选择发票日期');
    return;
  }
  if (!invoiceForm.value.invoiceNumber) {
    message.error('请输入发票号');
    return;
  }
  if (!invoiceForm.value.invoiceAmount || invoiceForm.value.invoiceAmount <= 0) {
    message.error('请输入有效的发票金额');
    return;
  }

  if (!currentInvoiceRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  invoiceLoading.value = true;

  // 调用uploadInvoice接口
  paymentFromApi
    .uploadInvoice({
      paymentId: currentInvoiceRecord.value.id,
      paymentCode: currentInvoiceRecord.value.receivePaymentCode,
      invoiceType: invoiceForm.value.invoiceType,
      invoiceDate: invoiceForm.value.invoiceDate,
      invoiceNumber: invoiceForm.value.invoiceNumber,
      invoiceAmount: invoiceForm.value.invoiceAmount,
    })
    .then(() => {
      message.success('发票上传成功');
      closeInvoiceModal();
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('上传发票失败:', error);
      message.error('上传发票失败，请重试');
    })
    .finally(() => {
      invoiceLoading.value = false;
    });
};

//发票
const invoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'name',
  },
  {
    title: '发票日期',
    dataIndex: 'age',
  },
  {
    title: '发票金额',
    dataIndex: '',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 详情页订单表格列
const detailOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
  },
  {
    title: '会议时间',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'receivePaymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 详情页发票表格列
const detailInvoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    align: 'center',
  },
  {
    title: '发票日期',
    dataIndex: 'invoiceDate',
    align: 'center',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 计算发票金额合计
const calculateInvoiceTotal = () => {
  if (!currentDetailRecord.value) return '0';
  const invoiceData = getInvoiceData();
  const total = invoiceData.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0);
  return `${total}元`;
};

// 获取发票数据
const getInvoiceData = () => {
  if (!currentDetailRecord.value || !currentDetailRecord.value.attachmentFiles) {
    return [];
  }
  // 这里根据实际业务逻辑筛选发票数据
  // 假设发票类型的 type 为特定值，需要根据实际情况调整
  return currentDetailRecord.value.attachmentFiles
    .filter((file: any) => file.type === 2) // 假设type=2为发票类型
    .map((file: any, index: number) => ({
      key: index,
      invoiceNumber: file.invoiceNumber || `85645433${index}`,
      invoiceDate: file.invoiceDate || '2025.1.21',
      invoiceAmount: file.invoiceAmount || 15000,
    }));
};

// 文件上传处理
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);

      // 确保文件被添加到列表中
      if (!fileList.value.some((f) => f.fileName === options.file.name)) {
        fileList.value.push(options.file);
      }
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 文件删除处理
const handleFileRemove = (file: any) => {
  const index = fileList.value.findIndex((f) => f.uid === file.uid);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
};

// 提交上传的付款凭证
const submitUpload = () => {
  if (fileList.value.length === 0) {
    message.error('请先上传付款凭证');
    return;
  }

  if (!currentDetailRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  // 提取文件路径
  const attachmentFiles = fileList.value.map((file) => file.filePath).filter(Boolean) as string[];

  if (attachmentFiles.length === 0) {
    message.error('文件上传未完成，请重试');
    return;
  }

  uploadLoading.value = true;

  // 调用confirmPaymentPayment接口
  paymentFromApi
    .uploadPaymentAttachment({
      id: currentDetailRecord.value.id,
      attachmentFile: attachmentFiles,
    })
    .then(() => {
      message.success('付款凭证上传成功');
      handleView(); // 关闭弹窗
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      });
    })
    .catch((error) => {
      console.error('提交付款凭证失败:', error);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};


// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string;
  switch (key) {
    case 'upload':
      openUploadModal(record);
      break;
    case 'uploadInvoice':
      openInvoiceModal(record);
      break;
    case 'resubmit':
      // 重新提交审批操作
      resubmitApproval(record);
      break;
    default:
      break;
  }
};

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = [];

  // 根据状态添加不同的操作选项
  if (record.status === PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD) {
    // 待上传支付凭证：显示上传支付凭证按钮
    options.push({
      key: 'upload',
      label: '上传付款凭证',
    });
  } else if (record.status === PaymentFromStatusEnum.PAYMENT_REJECTED) {
    // 收款驳回：可以重新上传支付凭证
    options.push({
      key: 'upload',
      label: '重新上传凭证',
    });
  } else if (record.status === PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD) {
    // 待国旅上传发票：不显示上传发票按钮
    // Do nothing - no options for this status
  } else if (record.status === PaymentFromStatusEnum.APPROVAL_REJECTED) {
    // 审批驳回：可以重新提交审批
    options.push({
      key: 'resubmit',
      label: '重新提交',
    });
  }
  // 审批中和已完成状态只显示查看，不需要额外的菜单选项

  return options;
};


// 重新提交审批操作
const resubmitApproval = (record: any) => {
  if (!record?.id) {
    message.error('记录ID不存在');
    return;
  }

  Modal.confirm({
    title: '重新提交审批',
    content: '确认重新提交该记录进行审批？',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      // 调用重新提交审批接口
      paymentFromApi
        .resubmitApproval({
          id: record.id,
        })
        .then(() => {
          message.success('重新提交审批成功');
          // 刷新列表
          listApiRun({
            ...searchParam.value,
            pageNum: data.value?.pageNum || 1,
            pageSize: data.value?.pageSize || 10,
          });
        })
        .catch((error) => {
          console.error('重新提交审批失败:', error);
        });
    },
  });
};
</script>

<template>
  <div class="page-container">
    <h-row :align="'middle'">
      <h-col :span="24" class="search-container">
        <h-row :align="'middle'" class="search-row">
          <!-- <h-col :span="2" class="search-label">
            <label for="serviceProvider">服务商：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.merchantCode" placeholder="请输入服务商" allow-clear />
          </h-col> -->

          <h-col :span="3" class="search-label">
            <label for="createTime">缴费单创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" class="full-width" allow-clear />
          </h-col>

          <h-col :span="2" class="search-label">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.status" placeholder="请选择状态" allow-clear class="full-width">
              <h-select-option :value="PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.PAYMENT_REJECTED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PAYMENT_REJECTED] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.APPROVAL_REJECTED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.APPROVAL_REJECTED] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.APPROVING">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.APPROVING] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.COMPLETED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.COMPLETED] }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" class="search-row">
          <h-col :span="24" class="button-row">
            <h-button class="button-margin" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ y: 550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
          class="main-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'receivePaymentCode'">
              <Tooltip :title="record.receivePaymentCode">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.receivePaymentCode }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'merchantName'">
              <Tooltip :title="record.merchantName">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.merchantName }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <h-tag :color="getStatusColor(record.status)">
                {{ PaymentFromStatusMap[record.status as keyof typeof PaymentFromStatusMap] || '未知状态' }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <div class="operator-buttons">
                <h-button type="link" @click="handleView(record)">查看</h-button>
                <Actions
                  v-if="getMenuOptions(record).length > 0"
                  :menu-options="getMenuOptions(record)"
                  :on-menu-click="(e) => handleMenuClick(record, e)"
                >
                </Actions>
              </div>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <!-- 删除了独立的上传付款凭证弹窗，已合并到详情弹窗中 -->

    <!-- 上传发票弹窗 -->
    <Modal v-model:open="invoiceModalVisible" title="上传发票" :footer="null" @cancel="closeInvoiceModal" width="500px">
      <div class="modal-content">
        <div class="modal-info-item"><strong>缴费单号：</strong>{{ currentInvoiceRecord?.receivePaymentCode }}</div>
        <div class="modal-info-item"><strong>服务商名称：</strong>{{ currentInvoiceRecord?.merchantName }}</div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票类型：</label>
          <h-select v-model:value="invoiceForm.invoiceType" class="invoice-form-input">
            <h-select-option :value="1">国旅</h-select-option>
            <h-select-option :value="2">服务商</h-select-option>
          </h-select>
        </div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票日期：<span class="required-mark">*</span></label>
          <h-date-picker
            v-model:value="invoiceForm.invoiceDate"
            class="invoice-form-input"
            placeholder="请选择发票日期"
            value-format="YYYY-MM-DD"
          />
        </div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票号：<span class="required-mark">*</span></label>
          <h-input v-model:value="invoiceForm.invoiceNumber" placeholder="请输入发票号" class="invoice-form-input" />
        </div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票金额：<span class="required-mark">*</span></label>
          <h-input-number
            v-model:value="invoiceForm.invoiceAmount"
            placeholder="请输入发票金额"
            class="invoice-form-input"
            :min="0"
            :precision="2"
          />
        </div>

        <div class="modal-footer">
          <h-button class="button-margin" @click="closeInvoiceModal">取消</h-button>
          <h-button type="primary" @click="submitInvoice" :loading="invoiceLoading">确定</h-button>
        </div>
      </div>
    </Modal>

    <!-- 缴费单详情/上传付款凭证弹窗 -->
    <Modal
      v-model:open="detailVisible"
      :title="viewMode === 'view' ? '缴费单详情' : '上传付款凭证'"
      :footer="null"
      @cancel="handleView()"
      width="800px"
      :loading="detailLoading"
    >
      <div v-if="currentDetailRecord" class="modal-content">
        <!-- 基本信息 -->
        <div class="modal-info">
          <div class="modal-info-item"><strong>缴费单号：</strong>{{ currentDetailRecord.receivePaymentCode }}</div>
          <div class="modal-info-item"><strong>服务商名称：</strong>{{ currentDetailRecord.merchantName }}</div>
          <div class="modal-info-item">
            <strong>付款总金额：</strong>{{ currentDetailRecord.receivePaymentAmount }}元
          </div>
          <!-- 驳回原因显示（当状态为驳回时） -->
          <div v-if="currentDetailRecord.status === PaymentFromStatusEnum.PAYMENT_REJECTED" class="modal-info-item">
            <strong>驳回原因：</strong>{{ currentDetailRecord.remark || '无' }}
          </div>
          <!-- 查看模式：显示已上传的付款凭证 -->
          <div v-if="viewMode === 'view'" class="modal-info-item">
            <strong>付款凭证：</strong>
            <template v-if="currentDetailRecord.attachmentFiles && currentDetailRecord.attachmentFiles.length > 0">
              <template v-for="(file, index) in currentDetailRecord.attachmentFiles" :key="index">
                <a :href="file.path" target="_blank" class="button-margin file-link">
                  {{ file.path ? file.path.split('/').pop() || `付款凭证${index + 1}` : `付款凭证${index + 1}` }}
                </a>
              </template>
            </template>
            <span v-else>无</span>
          </div>
        </div>

        <!-- 订单表格 -->
        <h-table
          :columns="detailOrderColumns"
          :data-source="currentDetailRecord.receivePaymentRecordsDetails || []"
          :pagination="false"
          size="small"
          bordered
          class="detail-table"
        >
        </h-table>

        <!-- 上传模式：显示上传区域和驳回原因 -->
        <div v-if="viewMode === 'upload'" class="modal-upload-section">
          <div class="modal-upload-item">
            <label class="invoice-form-label">付款凭证：</label>
            <h-upload
              v-model:fileList="fileList"
              :custom-request="uploadRequest"
              :multiple="true"
              :max-count="5"
              @remove="handleFileRemove"
              accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
              :show-upload-list="true"
            >
              <h-button :loading="uploadLoading">
                <UploadOutlined />
                上传文件
              </h-button>
            </h-upload>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="modal-footer">
          <!-- 查看模式：只显示确定按钮 -->
          <!-- <h-button v-if="viewMode === 'view'" type="primary" @click="handleView()">确定</h-button> -->
          <!-- 上传模式：显示确定和驳回按钮 -->
          <template v-if="viewMode === 'upload'">
            <h-button class="button-margin" @click="handleView()"> 取消 </h-button>
            <h-button type="primary" @click="submitUpload" :loading="uploadLoading"> 确定 </h-button>
          </template>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped lang="less">
// 页面容器
.page-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

// 查询条件行
.search-row {
  padding: 10px 10px 0px 10px;
}

// 标签样式
.search-label {
  text-align: right;
  padding-right: 10px;
}

// 按钮行
.button-row {
  text-align: right;
}

.button-row-left {
  text-align: left;
}

// 按钮间距
.button-margin {
  margin-right: 10px;
}

// 新增样式类
.search-container {
  margin-bottom: 10px;
}

.full-width {
  width: 100%;
}

.file-link {
  color: #1890ff;
}

.pagination-center {
  justify-content: center;
}

.detail-table {
  margin-top: 10px;
}

// 弹框内容
.modal-content {
  padding: 20px 0;
}

.modal-info {
  margin-bottom: 20px;
}

.modal-info-item {
  margin-bottom: 12px;
}

.modal-upload-section {
  margin-top: 20px;
  margin-bottom: 20px;
}

.modal-upload-item {
  margin-bottom: 16px;
}

.modal-textarea {
  margin-top: 8px;
}

.modal-footer {
  text-align: right;
  margin-top: 20px;
}

// 发票相关
.invoice-total {
  margin-bottom: 16px;
}

.invoice-form-item {
  margin-bottom: 16px;
}

.invoice-form-label {
  font-weight: bold;
}

.invoice-form-input {
  width: 100%;
  margin-top: 8px;
}

.required-mark {
  color: red;
}

.reject-reason-display {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 8px 12px;
  color: #cf1322;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 8px;
}

// 原有样式
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}
</style>

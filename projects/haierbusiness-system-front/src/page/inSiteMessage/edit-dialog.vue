<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, Textarea as hTextarea
} from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  IInSiteMessage
} from '@haierbusiness-front/common-libs';
import { inSiteMessageApi } from '@haierbusiness-front/apis';

interface Props {
  show: boolean;
  data: IInSiteMessage | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IInSiteMessage = {
  subject: '',
  content: '',
  applicationCode: '',
  id: null,
  isRead: ''
};

const rules = {

};

const inSiteMessage = ref<IInSiteMessage>({
  ...(props.data ? props.data : defaultData)
});

watch(
  () => props.data,
  (newData) => {
    inSiteMessage.value = {
      ...(newData ? newData : defaultData)
    };
  },
  { deep: true, immediate: true }
);


const emit = defineEmits(["cancel", "ok"]);

let visible = computed(() => props.show);

const getCurrentTime = () => {
  const now = new Date();
  return now.getFullYear() + '-' +
    String(now.getMonth() + 1).padStart(2, '0') + '-' +
    String(now.getDate()).padStart(2, '0') + ' ' +
    String(now.getHours()).padStart(2, '0') + ':' +
    String(now.getMinutes()).padStart(2, '0') + ':' +
    String(now.getSeconds()).padStart(2, '0');
}


</script>

<template>
  <h-modal v-model:visible="visible" :title="inSiteMessage.id ? '消息详情' : '新增站内信'" width="50%" @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading" @ok="$emit('cancel')">
    <table style="text-align: center;">
      <tr>
        <td class="td-style">来自：</td>
        <td>{{ inSiteMessage.applicationCode }}</td>
      </tr>
      <tr>
        <td class="td-style">内容：</td>
        <td>{{ inSiteMessage.content }}</td>
      </tr>
      <tr>
        <td class="td-style">相关链接：</td>
        <td><a :href="inSiteMessage.url">{{ inSiteMessage.url }}</a></td>
      </tr>
      <tr>
        <td class="td-style">发送时间：</td>
        <td>{{ inSiteMessage.gmtCreate }}</td>
      </tr>
      <tr>
        <td class="td-style">已读时间：</td>
        <td>{{ inSiteMessage.readTime ? inSiteMessage.readTime : getCurrentTime()}}</td>
      </tr>
    </table>
    <template #footer>
      <a-button @click="$emit('cancel')">关闭</a-button>
    </template>
  </h-modal>

</template>


<style lang="less" scoped>
.important {
  color: red;
}

table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  border: 1px solid #d4d4d4;
  /* 所有边框 */
  padding: 8px;
  text-align: left;
}

.td-style {
  text-align: right;
  width: 90px;
}
</style>
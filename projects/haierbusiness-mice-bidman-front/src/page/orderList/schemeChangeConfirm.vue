<script setup lang="ts">
import { ref, inject, onMounted, reactive } from 'vue';
import schemeInteract from '@haierbusiness-front/components/scheme/schemeInteract.vue';
import { meetingProcessOrchestration, resolveParam } from '@haierbusiness-front/utils';
import { Button, Dropdown, Menu, Pagination, BadgeRibbon, Tooltip, Popover, message, Modal } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import { miceBidManOrderListApi, schemeApi } from '@haierbusiness-front/apis';
import meetingDetail from '@haierbusiness-front/components/mice/orderList/meetingDetail.vue';
import billUploadScheme from '@haierbusiness-front/components/billUploadScheme/billUploadschemeDetails.vue';
const route = useRoute();
const frameModel = ref(inject<any>('frameModel'));
const routeQuery = reactive({
  record: resolveParam(route.query.record) || JSON.parse(route.query.record),
});

const priceChangeColumnsScheme = ref([
  {
    title: '切换前服务商',
    dataIndex: 'originalMerchants',
    width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '切换后服务商',
    dataIndex: 'newMerchants',
    width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '变更说明',
    dataIndex: 'reason',
    // width: 160,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '附件',
    dataIndex: 'doc',
    align: 'center',
    ellipsis: true,
  },
]);
const priceChangeListScheme = ref([]);
const showSwitchBidSchemeModal = async () => {
  let res = await schemeApi.switchRecord({
    miceId: routeQuery.record.miceId,
    originalSchemeId: routeQuery.record?.originalSchemeId,
  });
  priceChangeListScheme.value = res.slice(0, 1).map((item) => {
    // if (item.originalMerchants)
    //   item.originalMerchants =
    //     `<div>` + item.originalMerchants.map((item1) => item1.merchantName).join('</div><div>') + `</div>`;
    // else item.originalMerchants = '-';
    // if (item.newMerchants)
    //   item.newMerchants =
    //     `<div>` + item.newMerchants.map((item1) => item1.merchantName).join('</div><div>') + `</div>`;
    // else item.newMerchants = '-';
    let str = '';
    let document = {};
    item.schemeBidSwitchPaths.forEach((item1, index) => {
      try {
        document = JSON.parse(item1);
      } catch (error) {
        console.log(error);
      }

      str += `<a target='_blank' href='${document.url}'>${
        document.name
      }</a><span style='margin-right: 10px;color: #86909c' >${
        index == item.schemeBidSwitchPaths?.length - 1 ? '' : ','
      }</span>`;
    });
    item.doc = str;
    return item;
  });
};
onMounted(() => {
  frameModel.value = routeQuery.record.hideBtn === '1' ? 1 : 0;
  showSwitchBidSchemeModal();
});
</script>

<template>
  <div class="container">
    <div class="changeScheme" style="margin-bottom: 16px">
      <a-table
        :columns="priceChangeColumnsScheme"
        :data-source="priceChangeListScheme"
        :row-key="(record: { id: string; }) => record.id"
        :pagination="false"
        bordered
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <span v-if="column.dataIndex == 'doc'" v-html="record[column.dataIndex]"></span>
          <div v-else-if="['originalMerchants', 'newMerchants'].includes(column.dataIndex)">
            <div v-for="item in record[column.dataIndex]" :key="item.merchantId">
              <a-tooltip>
                <template #title>中标价格：{{ item?.winTheBidPrice }}元</template>
                {{ item.merchantName }}
              </a-tooltip>
            </div>
          </div>
          <span v-else>{{ record[column.dataIndex] }}</span>
        </template>
      </a-table>
    </div>
  </div>
</template>

<style scoped lang="less">
.container {
  background: #f1f2f6;
  padding: 0 auto;
  height: 100%;
}
.changeScheme {
  background: #fff;
  margin: 0 auto;
  width: 1280px !important;
  left: calc(50% - 640px);
  height: 100%;
}
.footer {
  border-top: 1px solid #f1f2f6;
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
  right: 0;
  background: #fff;
  left: calc(50% - 640px);
  z-index: 11;
  width: 1280px !important;
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
}
:deep(.ant-table-thead) {
  th {
    color: #86909c !important;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    font-style: normal;
  }
}
:deep(.ant-table-cell) {
  padding: 12px 8px !important;
  line-height: 20px;
}
</style>

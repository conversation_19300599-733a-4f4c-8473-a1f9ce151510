<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Modal as hModal,
  Upload as hUpload,
  Popconfirm as hPopconfirm,
  message,
  Form as hForm,
  FormItem as hFormItem,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined, UploadOutlined, InboxOutlined } from '@ant-design/icons-vue';
import { assessmentItemsApi, fileApi } from '@haierbusiness-front/apis';
import {
  IAssessmentItemsFilter,
  IAssessmentItems,
  SupplierTypeLabel,
  SupplierType,
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, reactive, nextTick, h } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import router from '../../router'
import ColumnFilter from '@haierbusiness-front/components/mice/search/ColumnFilter.vue';
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  // 页面加载时获取数据
  fetchList({ pageNum: 1, pageSize: 10 })
})

const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '100px',
    align: 'center',
    customRender: ({ index }) => {
      return (data.value?.pageNum! - 1) * data.value?.pageSize! + index + 1;
    },
  },
  {
    title: '考核条目名称',
    dataIndex: 'name',
    width: '220px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '考核条目规则',
    dataIndex: 'detail',
    width: '300px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商类型',
    dataIndex: 'type',
    width: '150px',
    align: 'center',
    customRender: ({ text }) => {
      return SupplierTypeLabel[text as SupplierType] || '未知类型';
    },
  },
  {
    title: '分数',
    dataIndex: 'score',
    width: '80px',
    align: 'center',
  },
  {
    title: '金额',
    dataIndex: 'money',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => text != null ? `${text.toFixed(2)}元` : '',
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => {
      return text === 10 ? '启用' : text === 20 ? '停用' : '';
    },
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '150px',
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '150px',
    fixed: 'right',
    align: 'center'
  },
];

// 处理状态枚举
const statusOptions = [
  { label: '全部', value: '' },
  { label: '启用', value: 10 },
  { label: '停用', value: 20 },
];

// 服务商类型枚举使用表格中的枚举
const supplierTypeOptions = Object.entries(SupplierTypeLabel).map(([value, label]) => ({
  value,
  label,
}));

const searchParam = ref<IAssessmentItemsFilter>({
  name: '',
  type: undefined,
  state: undefined,
  score: undefined,
  money: undefined,
  createName: '',

  gmtCreateStart: '',
  gmtCreateEnd: '',
  pageNum: 1,
  pageSize: 10
})

// 使用useRequest代替usePagination，手动处理分页
const { data, loading, run: fetchList } = useRequest(assessmentItemsApi.list, {
  manual: true, // 手动触发请求
  defaultParams: [{ pageNum: 1, pageSize: 10 }]
});

const reset = () => {
  searchParam.value = {
    name: '',
    type: undefined,
    state: undefined,
    score: undefined,
    money: undefined,
    createName: '',
    detail: '',
    gmtCreateStart: '',
    gmtCreateEnd: '',
    pageNum: 1,
    pageSize: 10
  }
  createTimeRange.value = undefined
  handleSearch()
  console.log('重置后的搜索参数:', searchParam.value);

  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    console.log('重置后调用接口参数:', params);
    fetchList(params);
  });
}

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
  onChange: (page: number, pageSize: number) => {
    searchParam.value.pageNum = page
    searchParam.value.pageSize = pageSize
    handleSearch()
  }
}));

// 修复参数类型
const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  searchParam.value.pageNum = pagination.current
  searchParam.value.pageSize = pagination.pageSize
  // handleSearch()
  // 确保使用最新的搜索参数和过滤参数，优先使用filterInputs中的非空值
  const params = {
    ...searchParam.value,
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
  };

  console.log('最终查询参数:', params);
  fetchList(params);
};
// 创建时间范围
const createTimeRange = ref<[Dayjs, Dayjs]>()
watch(() => createTimeRange.value, (n: any) => {
  if (n) {
    searchParam.value.gmtCreateStart = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.gmtCreateEnd = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.gmtCreateStart = undefined
    searchParam.value.gmtCreateEnd = undefined
  }
});
// 处理查询条件，只传递有值的参数
const getSearchParams = () => {
  const params: Record<string, any> = {
    pageNum: searchParam.value.pageNum,
    pageSize: searchParam.value.pageSize
  }

  // 只添加有值的查询条件
  if (searchParam.value.name) {
    params.name = searchParam.value.name
  }

  if (searchParam.value.type) {
    params.type = searchParam.value.type
  }

  if (searchParam.value.state) {
    params.state = searchParam.value.state
  }
  if (searchParam.value.detail) {
    params.detail = searchParam.value.detail
  }

  if (searchParam.value.createName) {
    params.createName = searchParam.value.createName
  }


  if (searchParam.value.gmtCreateStart) {
    params.gmtCreateStart = searchParam.value.gmtCreateStart
  }

  if (searchParam.value.gmtCreateEnd) {
    params.gmtCreateEnd = searchParam.value.gmtCreateEnd
  }
  if (searchParam.value.score) {
    const numValue = Number(searchParam.value.score);
    if (!isNaN(numValue)) {
      params.score = numValue;
    } else {
      message.error('分数必须为数字'); // 或抛出错误
    }
  }

  if (searchParam.value.money) {
    const numValue = Number(searchParam.value.money);
    if (!isNaN(numValue)) {
      params.money = numValue;
    } else {
      message.error('金额必须为数字'); // 或抛出错误
    }
  }


  return params
}

// 处理查询按钮点击
const handleSearch = () => {
  // 直接获取参数并查询，不进行表单验证
  const params = getSearchParams()
  fetchList(params)
  // searchParam.value = {
  //   name: '',
  //   type: undefined,
  //   state: undefined,
  //   score: undefined,
  //   money: undefined,
  //   createName: '',
  //   gmtCreateStart: '',
  //   gmtCreateEnd: '',
  //   pageNum: 1,
  //   pageSize: 10
  // }
};

const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<IAssessmentItems, IAssessmentItems>(assessmentItemsApi, "考核条目维护", () => {
    // 保存后刷新列表
    handleSearch()
  })

const thisHandleEdit = (item: IAssessmentItems) => {
  const currentData = {
    ...item
  };
  handleEdit({ ...currentData });
}

// 删除
const { handleDelete } = useDelete(assessmentItemsApi, () => {
  // 删除后刷新列表
  handleSearch()
})



// 添加导入弹框相关状态
const importVisible = ref(false);
const importMode = ref<boolean>(false);
const importFileList = ref<any[]>([]);
const uploadLoading = ref(false);
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const selectedFile = ref<File | null>(null);
const fileInputRef = ref<HTMLInputElement | null>(null);

// 上传附件和导入
const uploadRequest = () => {
  if (!selectedFile.value) {
    message.error('请先选择文件');
    return;
  }

  uploadLoading.value = true;

  const formData = new FormData();
  formData.append("file", selectedFile.value);
  formData.append("isAdd", importMode.value.toString());

  assessmentItemsApi.importItems(formData as any)
    .then(res => {
      message.success(`考核条目${importMode.value ? '覆盖' : '追加'}导入成功`);
      importVisible.value = false;
      handleSearch();
    })
    .catch((error) => {
      console.error('导入失败:', error);
      message.error('导入失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 选择文件
const handleFileChange = (e: Event) => {
  const input = e.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    selectedFile.value = input.files[0];
  }
};

// 触发文件选择
const triggerFileSelect = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

const openImportModal = () => {
  importVisible.value = true;
  importMode.value = false;
  selectedFile.value = null;
};

// 处理启用/停用状态变更
const handleStateChange = (id: number, state: number) => {
  assessmentItemsApi.itemState({ id, state })
    .then(() => {
      message.success(state === 10 ? '启用成功' : '停用成功');
      handleSearch();
    })
    .catch(() => {
      message.error(state === 10 ? '启用失败' : '停用失败');
    });
};

// 表单引用
const from = ref()



</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right; padding-right: 10px; flex: 0 0 9.5%;max-width: 9.3%;">
            <label for="name">考核条目名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="name" v-model:value="searchParam.name" placeholder="请输入考核条目名称" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px;flex: 0 0 9.333333%;max-width: 9.333333%;">
            <label for="type">服务商类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="type" v-model:value="searchParam.type" placeholder="请选择服务商类型" style="width: 100%" allow-clear>
              <h-select-option v-for="item in supplierTypeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="1" style="text-align: right; padding-right: 10px" class="state">
            <label for="state">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="state" v-model:value="searchParam.state" placeholder="请选择状态" style="width: 100%" allow-clear>
              <h-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right:10px;">
            <label for="createName">创建人：</label>
          </h-col>
          <h-col :span="3">
            <h-input id="createName" v-model:value="searchParam.createName" placeholder="请输入创建人" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right; padding-right: 10px;flex: 0 0 9.333333%;max-width: 9.333333%;">
            <label for="createTime">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker id="createTime" v-model:value="createTimeRange" value-format="YYYY-MM-DD"
              style="width: 100%" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px" class="Status">
            <label for="detail">考核条目规则：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="detail" v-model:value="searchParam.detail" placeholder="请输入考核条目规则" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px" class="score">
            <label for="score">分数：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="score" v-model:value="searchParam.score" placeholder="请输入分数" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="money">金额：</label>
          </h-col>
          <h-col :span="3">
            <h-input id="money" v-model:value="searchParam.money" placeholder="请输入金额" allow-clear />
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;margin: 15px 0;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleSearch" style="margin-right: 10px">
              <SearchOutlined />查询
            </h-button>

          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;margin: 15px 0;">
          <h-col :span="24">
            <h-button type="primary" @click="handleCreate()">
              <PlusOutlined /> 新增
            </h-button>
            <h-button type="primary" style="margin-left: 10px;" @click="openImportModal()">
              <UploadOutlined /> 导入
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ x: 1600 }" :loading="loading" @change="handleTableChange">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="thisHandleEdit(record)">编辑</h-button>
              <h-popconfirm v-if="record.state === 10" title="确定要停用该考核条目吗？" @confirm="handleStateChange(record.id, 20)">
                <h-button type="link">停用</h-button>
              </h-popconfirm>
              <h-popconfirm v-if="record.state === 20" title="确定要启用该考核条目吗？" @confirm="handleStateChange(record.id, 10)">
                <h-button type="link">启用</h-button>
              </h-popconfirm>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleOk">
      </edit-dialog>
    </div>

    <!-- 导入弹框 -->
    <h-modal :visible="importVisible" title="导入考核条目" @cancel="importVisible = false" :footer="null" width="500px">
      <div style="padding: 20px;">
        <div style="margin-bottom: 16px;">
          <h-select :value="importMode" @change="(val) => importMode = val" style="width: 100%;">
            <h-select-option :value="false">追加</h-select-option>
            <h-select-option :value="true">覆盖</h-select-option>
          </h-select>
        </div>

        <div style="margin-top: 16px;">
          <div style="display: flex; align-items: center; margin-bottom: 16px;">
            <input ref="fileInputRef" type="file" style="display: none;" accept=".xlsx,.xls,.csv"
              @change="handleFileChange" />
            <h-button type="primary" @click="triggerFileSelect" style="margin-right: 8px;">
              <UploadOutlined /> 选择文件
            </h-button>

          </div>
          <span v-if="selectedFile">{{ selectedFile.name }}</span>
          <div style="color: #999; font-size: 12px; margin-bottom: 16px;">
            支持 .xlsx, .xls, .csv 格式
          </div>
          <div>
            <a href="../../../src/assets/template/考核条目.xlsx">下载模板</a>
          </div>
        </div>

        <div style="margin-top: 16px; text-align: right;">
          <h-button @click="importVisible = false" style="margin-right: 8px;">取消</h-button>
          <h-button type="primary" @click="uploadRequest" :loading="uploadLoading" :disabled="!selectedFile">
            确认上传
          </h-button>
        </div>
      </div>
    </h-modal>
  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.upload-area {
  padding: 24px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  text-align: center;
  background: #fafafa;
  cursor: pointer;
  transition: border-color 0.3s;

  &:hover {
    border-color: #40a9ff;
  }
}

.ant-upload-text {
  margin: 8px 0;
  font-size: 16px;
}

.ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
}

.Status {
  padding-right: 0px;
  display: block;
  flex: 0 0 10.9%;
  max-width: 9.3%;
}

.score,
.state {
  display: block;
  flex: 0 0 7.8%;
  max-width: 7.6%;
}
</style>

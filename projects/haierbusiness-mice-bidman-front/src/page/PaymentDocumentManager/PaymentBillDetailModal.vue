<!-- 缴费单详情弹框组件 -->
<script setup lang="ts">
import { Table as hTable, Tabs as ATabs, TabPane as ATabPane, Modal } from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { computed, ref, watch, onMounted, inject } from 'vue';
import { useRoute } from 'vue-router';
import { DataType } from 'vue-request';
import { resolveParam } from '@haierbusiness-front/utils';
import { paymentFromApi } from '@haierbusiness-front/apis';
import { message } from 'ant-design-vue';

// 定义Props
interface Props {
  visible?: boolean;
  detailData?: any;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  loading: false,
});

// 定义Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  close: [];
}>();

// 路由检测
const route = useRoute();
const routeRecord = route.query.record ? resolveParam(route.query.record as string) : null;
const hideBtn = routeRecord?.hideBtn || '';
const isPageMode = computed(() => hideBtn === '1');
const frameModel = inject<any>('frameModel');
  frameModel.value = hideBtn === '1' ? 1 : 0; // frameModel-1,隐藏;0-显示
// 响应式数据
const activeKey = ref('1');

// 页面模式下的数据状态
const pageDetailData = ref<any>(null);
const pageLoading = ref(false);

// 计算实际使用的数据和加载状态
const actualDetailData = computed(() => {
  return isPageMode.value ? pageDetailData.value : props.detailData;
});

const actualLoading = computed(() => {
  return isPageMode.value ? pageLoading.value : props.loading;
});

// 页面模式下获取详情数据
const fetchDetailData = async () => {
  if (!isPageMode.value || !routeRecord?.id) return;

  pageLoading.value = true;
  try {
    const res = await paymentFromApi.getPaymentDetails(routeRecord.id);
    pageDetailData.value = res;
  } catch (error) {
    console.error('获取详情失败:', error);
    message.error('获取详情失败，请重试');
  } finally {
    pageLoading.value = false;
  }
};

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value);
  },
});

// 监听弹窗关闭
const handleCancel = () => {
  emit('close');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDetailData();
});

// 详情页订单表格列
const detailOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
  },
  {
    title: '会议时间',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'receivePaymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 发票类型映射
const InvoiceTypeMap = {
  1: '国旅',
  2: '服务商',
};

// 详情页发票表格列
const detailInvoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    align: 'center',
  },
  {
    title: '发票日期',
    dataIndex: 'invoiceDate',
    align: 'center',
  },
  {
    title: '发票类型',
    dataIndex: 'invoiceType',
    align: 'center',
    customRender: ({ text }) => InvoiceTypeMap[text as keyof typeof InvoiceTypeMap] || '未知类型',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 计算发票金额合计
const calculateInvoiceTotal = () => {
  if (!actualDetailData.value) return '0';
  const invoiceData = getInvoiceData();
  const total = invoiceData.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0);
  return `${total}元`;
};

// 获取发票数据
const getInvoiceData = () => {
  if (!actualDetailData.value || !actualDetailData.value.miceInvoiceQueryDetails) {
    return [];
  }
  // 使用miceInvoiceQueryDetails作为发票数据源
  return actualDetailData.value.miceInvoiceQueryDetails.map((invoice: any, index: number) => ({
    key: invoice.id || index,
    invoiceNumber: invoice.invoiceNumber || '',
    invoiceDate: invoice.invoiceDate || '',
    invoiceAmount: invoice.invoiceAmount || 0,
    invoiceType: invoice.invoiceType || 1,
    isConfirmed: invoice.isConfirmed || 0,
  }));
};

// 监听弹窗打开，重置tab
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      activeKey.value = '1';
    }
  },
);
</script>

<template>
  <!-- 页面模式 -->
  <div v-if="isPageMode" class="page-detail-container">
    <div v-if="actualDetailData" class="detail-content">
      <!-- 基本信息 -->
      <div class="detail-info">
        <h3>缴费单详情</h3>
        <div class="detail-info-item"><strong>收款单号：</strong>{{ actualDetailData.receivePaymentCode }}</div>
        <div class="detail-info-item"><strong>服务商名称：</strong>{{ actualDetailData.merchantName }}</div>
        <div class="detail-info-item"><strong>付款总金额：</strong>{{ actualDetailData.receivePaymentAmount }}元</div>
        <div class="detail-info-item">
          <strong>付款凭证：</strong>
          <template v-if="actualDetailData.attachmentFiles && actualDetailData.attachmentFiles.length > 0">
            <template v-for="(file, index) in actualDetailData.attachmentFiles" :key="index">
              <a :href="file.path" target="_blank" class="file-link">
                {{ file.path ? file.path.split('/').pop() || `付款凭证${index + 1}` : `付款凭证${index + 1}` }}
              </a>
            </template>
          </template>
          <span v-else>无</span>
        </div>
      </div>

      <!-- Tab页 -->
      <a-tabs v-model:activeKey="activeKey" class="detail-tabs">
        <a-tab-pane key="1" tab="订单">
          <h-table
            :columns="detailOrderColumns"
            :data-source="actualDetailData.receivePaymentRecordsDetails || []"
            :pagination="false"
            size="small"
            bordered
            class="detail-table"
          >
          </h-table>
        </a-tab-pane>
        <a-tab-pane key="2" tab="发票">
          <div class="invoice-total"><strong>发票金额合计：</strong>{{ calculateInvoiceTotal() }}</div>
          <h-table
            :columns="detailInvoiceColumns"
            :data-source="getInvoiceData()"
            :pagination="false"
            size="small"
            bordered
            class="detail-table"
          >
          </h-table>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div v-else-if="actualLoading" class="loading-container">
      <div style="text-align: center; padding: 50px">
        <a-spin size="large" />
        <div style="margin-top: 16px">加载中...</div>
      </div>
    </div>
  </div>

  <!-- 弹框模式 -->
  <Modal
    v-else
    v-model:open="modalVisible"
    title="缴费单详情"
    :footer="null"
    @cancel="handleCancel"
    width="800px"
    :loading="loading"
  >
    <div v-if="detailData" class="modal-content">
      <!-- 基本信息 -->
      <div class="modal-info">
        <div class="modal-info-item"><strong>收款单号：</strong>{{ detailData.receivePaymentCode }}</div>
        <div class="modal-info-item"><strong>服务商名称：</strong>{{ detailData.merchantName }}</div>
        <div class="modal-info-item"><strong>付款总金额：</strong>{{ detailData.receivePaymentAmount }}元</div>
        <div class="modal-info-item">
          <strong>付款凭证：</strong>
          <template v-if="detailData.attachmentFiles && detailData.attachmentFiles.length > 0">
            <template v-for="(file, index) in detailData.attachmentFiles" :key="index">
              <a :href="file.path" target="_blank" class="button-margin" style="color: #1890ff">
                {{ file.path ? file.path.split('/').pop() || `付款凭证${index + 1}` : `付款凭证${index + 1}` }}
              </a>
            </template>
          </template>
          <span v-else>无</span>
        </div>
      </div>

      <!-- Tab页 -->
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="订单">
          <h-table
            :columns="detailOrderColumns"
            :data-source="detailData.receivePaymentRecordsDetails || []"
            :pagination="false"
            size="small"
            bordered
            class="detail-table"
          >
          </h-table>
        </a-tab-pane>
        <a-tab-pane key="2" tab="发票">
          <div class="invoice-total"><strong>发票金额合计：</strong>{{ calculateInvoiceTotal() }}</div>
          <h-table
            :columns="detailInvoiceColumns"
            :data-source="getInvoiceData()"
            :pagination="false"
            size="small"
            bordered
            class="detail-table"
          >
          </h-table>
        </a-tab-pane>
      </a-tabs>
    </div>
  </Modal>
</template>

<style scoped lang="less">
// 页面模式样式
.page-detail-container {
  background-color: #fff;
  height: 100vh;
  width: 100%;
  padding: 20px;
  overflow: auto;
}

.detail-content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-info {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;

  h3 {
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;
    display: flex;
    align-items: center;

    span {
      display: inline-block;
      width: 4px;
      height: 20px;
      margin-right: 8px;
      background: #1868db;
    }
  }
}

.detail-info-item {
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }
}

.file-link {
  color: #1890ff;
  margin-right: 12px;
  text-decoration: none;

  &:hover {
    color: #40a9ff;
    text-decoration: underline;
  }
}

.detail-tabs {
  margin-top: 8px;

  :deep(.ant-tabs-content-holder) {
    padding-top: 16px;
  }
}

// 弹框内容
.modal-content {
  padding: 20px 0;
}

.modal-info {
  margin-bottom: 20px;
}

.modal-info-item {
  margin-bottom: 12px;
}

// 表格样式
.detail-table {
  margin-top: 10px;

  :deep(.ant-table) {
    font-size: 14px;
  }
}

// 发票相关
.invoice-total {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
}

// 按钮间距
.button-margin {
  margin-right: 10px;
}

// 表格布局
:deep(table) {
  table-layout: auto !important;
}
</style>

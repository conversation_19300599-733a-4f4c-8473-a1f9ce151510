<script setup lang="ts">
// 方案互动-住宿方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, inject, defineProps, defineEmits, defineExpose } from 'vue';
import { throttle } from 'lodash';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import {
  hotelLevelAllConstant,
  RoomTypeConstant,
  BreakfastTypeConstant,
  HotelsArr,
  schemeStaysArr,
} from '@haierbusiness-front/common-libs';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload / 中标方案查看-bidSchemeView
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  demandHotels: {
    type: Array,
    default: [],
  },
  hotels: {
    type: Array,
    default: [],
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  processNode: {
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  merchantType: {
    type: Number,
    default: null,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeStaysEmit']);

const schemeChangeAddFunc = inject('schemeChangeAddFunc', () => {});
const schemeChangeDelFunc = inject('schemeChangeDelFunc', () => {});

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const hotelList = ref<array>([]);
const schemeDifferenceStays = ref<array>([]);

// 住宿
const staysParams = ref<schemeStaysArr>({
  tempId: null, //
  tempSchemeHotelId: null, //
  miceDemandPushHotelId: null, //
  demandDate: '', // 需求日期
  roomType: null, // 房型
  breakfastType: 1, // 早餐类型
  personNum: null, // 人数
  schemeRoomNum: null, // 入住房间数
  discrepancyReason: '', // 不一致原因
  description: '', // 备注

  schemeUnitPrice: null,
});

// 住宿方案
const schemePlanLabelList = ['酒店选择', '房型', '人数', '房间数', '不一致原因', '备注'];
// 酒店差异
const stayDifferenceTable = ['', '当日总人数', '需求差异', '大床房间夜数', '双床房间夜数', '套房间夜数'];
// 需求差异
const demandTable = ref<array>([]);
// 方案差异
const planTable = ref<array>([]);
const diffColor = ref<array>([]);
const isShowDiff = ref<boolean>(false);

const diffRemarks = ref<string>(''); // 差异原因
const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 差异
watch(
  () => newSchemeList.value,
  (newObj) => {
    if (newObj?.length > 0) {
      let td1 = 0;
      let td2 = '';
      let td3 = 0;
      let td4 = 0;
      let td5 = 0;

      newObj.forEach((e) => {
        td1 += e.personNum || 0;

        td2 =
          (td2 ? td2 + '，' : '') +
          (RoomTypeConstant.ofType(e.roomType)?.desc || '-') +
          '/' +
          (BreakfastTypeConstant.ofType(e.breakfastType)?.desc || '-') +
          '*' +
          (e.schemeRoomNum || 0);

        if (e.roomType === 1) {
          // 大床房（默认1人）
          td3 += e.schemeRoomNum;
        }
        if (e.roomType === 2) {
          // 双床房
          td4 += e.schemeRoomNum;
        }
        if (e.roomType === 3) {
          // 套房（默认1人）
          td5 += e.schemeRoomNum;
        }
      });

      // 需求差异
      planTable.value = ['方案', td1, td2, td3, td4, td5];

      diffColor.value = planTable.value.map((e, i) => {
        return demandTable.value[i] === e;
      });
      diffColor.value[0] = true;

      isShowDiff.value = !diffColor.value.every((e) => e === true);

      // 价格计算
      priceCalcFun();
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  () => [props.hotels, props.merchantType, newSchemeList.value],
  () => {
    hotelList.value = [...props.hotels] || [];

    newSchemeList.value.forEach((e) => {
      if (props.hotels && props.hotels.length === 1) {
        // 是否直签酒店
        e.tempSchemeHotelId =
          props.merchantType !== 1 ? props.hotels[0].tempId : e.miceSchemeHotelId || props.hotels[0].tempId;
        e.miceDemandPushHotelId = props.hotels[0].miceDemandPushHotelId;
      } else {
        // if (e.miceSchemeHotelId) e.tempSchemeHotelId = e.miceSchemeHotelId;
      }
    });
  },
  {
    immediate: true,
    deep: true,
  },
);

// 酒店名称
const hotelName = (hotelItem: HotelsArr) => {
  let str = '-';

  props.demandHotels.forEach((e, index) => {
    if (e.id && e.id === hotelItem.miceDemandHotelId) {
      str = `酒店${index + 1}(${
        (e.centerMarker ? e.centerMarker : e.provinceName + e.cityName + e.districtNames) +
        '/' +
        (hotelLevelAllConstant.ofType(e.level)?.desc || '-')
      })`;
    }
  });

  return str;
};

const changePrice = throttle((index: number) => {
  // // 价格计算
  // priceCalcFun();
}, 500);

// 价格计算
const priceCalcFun = () => {
  const isAllPriceWrite = newSchemeList.value.every(
    (e) =>
      e.schemeRoomNum !== null &&
      e.schemeRoomNum !== undefined &&
      e.schemeUnitPrice !== null &&
      e.schemeUnitPrice !== undefined,
  );

  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.schemeRoomNum * e.schemeUnitPrice;
    });

    emit('schemePriceEmit', { type: 'stay', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  }
};

// 房间数
const changeRoomNum = (i: Number) => {
  // 大床房（默认1人） - 1
  // 双床房 - 2
  // 套房（默认1人） - 3

  if (newSchemeList.value[i]?.roomType && newSchemeList.value[i]?.personNum) {
    // 房间容纳人数
    const roomPersonNum = newSchemeList.value[i]?.roomType === 2 ? 2 : 1;

    const roomNums = newSchemeList.value[i]?.personNum / roomPersonNum;
    newSchemeList.value[i].schemeRoomNum = roomNums ? Math.ceil(roomNums) : null;

    if (
      Number(newSchemeList.value[i].personNum) ===
      Number(newSchemeList.value[i].schemeRoomNum) *
        (newSchemeList.value[i].roomType === 1 || newSchemeList.value[i].roomType === 3 ? 1 : 2)
    ) {
      // 人数、房间数匹配时，清空不一致原因
      newSchemeList.value[i].discrepancyReason = '';
    }
    // } else {
    //   newSchemeList.value[i].schemeRoomNum = null;
  }
};

const addScheme = (idx: number) => {
  newSchemeList.value.push({
    ...staysParams.value,
    tempId: Date.now() + idx,
    tempSchemeHotelId: hotelList.value && hotelList.value.length === 1 ? hotelList.value[0].tempId : null,
    miceDemandStayId: Date.now() + idx,

    demandDate: oldSchemeList.value[0]?.demandDate || '',
    miceDemandPushHotelId:
      hotelList.value && hotelList.value.length === 1 ? hotelList.value[0].miceDemandPushHotelId : null,
  });

  schemeChangeAddFunc();

  // priceCalcFun();
};
const delScheme = (idx: number) => {
  newSchemeList.value.splice(idx, 1);

  schemeChangeDelFunc();
  // priceCalcFun();
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 住宿差异
const staysDiffFunc = () => {
  // 住宿差异
  let differenceDate = oldSchemeList.value[0]?.demandDate || newSchemeList.value[0]?.demandDate || ''; // 差异日期
  let demandTotalPerson = 0; // 需求当日总人数
  let schemeTotalPerson = 0; // 方案当日总人数

  let demandRoomTypeList = []; // 需求房型统计
  let schemeRoomTypeList = []; // 方案房型统计
  let demandRoomType = ''; // 需求房型统计
  let schemeRoomType = ''; // 方案房型统计
  let demandOneRooms = 0; // 	需求大床数量
  let schemeOneRooms = 0; // 方案大床数量
  let demandTwoRooms = 0; // 需求双床数量
  let schemeTwoRooms = 0; // 方案双床数量
  let demandSuiteRooms = 0; // 需求套床数量
  let schemeSuiteRooms = 0; // 方案套床数量

  let reason = ''; // 差异原因
  let demandStayIdList = []; // 需求住宿
  let demandStayIds = ''; // 需求住宿

  // 需求
  oldSchemeList.value?.forEach((e) => {
    demandTotalPerson += e.personNum;
    demandRoomTypeList.push(e.roomType);

    demandStayIdList.push(e.id);

    if (e.roomType === 1) {
      // 大床房（默认1人）
      demandOneRooms += e.schemeRoomNum || e.roomNum;
    }
    if (e.roomType === 2) {
      // 双床房
      demandTwoRooms += e.schemeRoomNum || e.roomNum;
    }
    if (e.roomType === 3) {
      // 套房（默认1人）
      demandSuiteRooms += e.schemeRoomNum || e.roomNum;
    }
  });

  demandRoomTypeList = Array.from(new Set(demandRoomTypeList));
  demandRoomType = demandRoomTypeList.join(',');

  demandStayIdList = Array.from(new Set(demandStayIdList));
  demandStayIds = demandStayIdList.join(',');

  // 方案
  newSchemeList.value?.forEach((e) => {
    schemeTotalPerson += e.personNum;
    schemeRoomTypeList.push(e.roomType);

    if (e.roomType === 1) {
      // 大床房（默认1人）
      schemeOneRooms += e.schemeRoomNum;
    }
    if (e.roomType === 2) {
      // 双床房
      schemeTwoRooms += e.schemeRoomNum;
    }
    if (e.roomType === 3) {
      // 套房（默认1人）
      schemeSuiteRooms += e.schemeRoomNum;
    }
  });

  schemeRoomTypeList = Array.from(new Set(schemeRoomTypeList));
  schemeRoomType = schemeRoomTypeList.join(',');

  const tempIdList = newSchemeList.value.filter((e) => e.tempId);
  const idsList = tempIdList.map((e) => {
    return e.tempId;
  });
  const schemeTempStayIds = idsList.join(',');

  schemeDifferenceStays.value = [];

  if (
    isShowDiff.value ||
    demandTotalPerson !== schemeTotalPerson ||
    demandOneRooms !== schemeOneRooms ||
    demandTwoRooms !== schemeTwoRooms ||
    demandSuiteRooms !== schemeSuiteRooms
  ) {
    schemeDifferenceStays.value = [
      {
        schemeTempStayIds: schemeTempStayIds,
        demandStayIds: demandStayIds,
        differenceDate: differenceDate,

        demandTotalPerson: demandTotalPerson,
        schemeTotalPerson: schemeTotalPerson,
        demandRoomType: demandRoomType,
        schemeRoomType: schemeRoomType,

        demandOneRooms: demandOneRooms || 0,
        schemeOneRooms: schemeOneRooms || 0,
        demandTwoRooms: demandTwoRooms || 0,
        schemeTwoRooms: schemeTwoRooms || 0,
        demandSuiteRooms: demandSuiteRooms || 0,
        schemeSuiteRooms: schemeSuiteRooms || 0,
        reason: diffRemarks.value,
      },
    ];
  }
};

// 暂存
const stayTempSave = () => {
  staysDiffFunc();

  emit('schemeStaysEmit', {
    schemeStays: [...newSchemeList.value],
    schemeDifferenceStays: [...schemeDifferenceStays.value],
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const staySub = () => {
  let isStayVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    isVerifyFailed.value = true;

    if (isStayVerPassed === false) return;

    if (!e.tempSchemeHotelId) {
      message.error('请选择' + e.demandDate + '住宿' + (i + 1) + '酒店');

      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }

    if (e.roomType === null || e.roomType === undefined) {
      message.error('请选择' + e.demandDate + '住宿' + (i + 1) + '房型');

      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }

    if (e.breakfastType === null || e.breakfastType === undefined) {
      message.error('请选择' + e.demandDate + '住宿' + (i + 1) + '早餐类型');

      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }

    if (!e.personNum) {
      message.error('请输入' + e.demandDate + '住宿' + (i + 1) + '人数');

      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }

    if (!e.schemeRoomNum) {
      message.error('请输入' + e.demandDate + '住宿' + (i + 1) + '房间数');

      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }

    if (
      Number(e.personNum) !== Number(e.schemeRoomNum) * (e.roomType === 1 || e.roomType === 3 ? 1 : 2) &&
      !e.discrepancyReason
    ) {
      message.error('请输入' + e.demandDate + '住宿' + (i + 1) + '不一致原因');

      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }

    if (!e.schemeUnitPrice) {
      message.error('请输入' + e.demandDate + '住宿' + (i + 1) + '单价');

      isStayVerPassed = false;
      anchorJump('schemeStayId' + e.demandDate + i);
      return;
    }
  });

  staysDiffFunc();

  if (
    schemeDifferenceStays.value.length > 0 &&
    !diffRemarks.value &&
    isStayVerPassed &&
    isShowDiff.value &&
    props.schemeType !== 'notBidding'
  ) {
    message.error('请选择' + differenceDate + '住宿差异原因');

    isStayVerPassed = false;
    anchorJump('schemeStayDiffId' + newSchemeList.value[0].demandDate);
  }

  if (isStayVerPassed) {
    stayTempSave();
  }

  return isStayVerPassed;
};

defineExpose({ staySub, stayTempSave });

onMounted(async () => {
  if (props.schemeItem && props.schemeItem.stays) {
    oldSchemeList.value = JSON.parse(JSON.stringify(props.schemeItem))?.stays || [];

    // console.log('%c [ 住宿 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', oldSchemeList.value);

    if (oldSchemeList.value && oldSchemeList.value.length > 0) {
      if (props.isSchemeCache && props.schemeCacheItem) {
        // 缓存 - 反显
        newSchemeList.value = props.schemeCacheItem?.stays || [];

        newSchemeList.value.forEach((e) => {
          if (e.miceSchemeHotelId) e.tempSchemeHotelId = e.miceSchemeHotelId;
        });
        // if (hotelList.value && hotelList.value.length === 1) {
        //   newSchemeList.value.forEach((e) => {
        //     e.tempSchemeHotelId = hotelList.value[0].miceDemandPushHotelId;
        //     e.miceDemandPushHotelId = hotelList.value[0].miceDemandPushHotelId;
        //   });
        // }

        // 差异原因
        diffRemarks.value = props.schemeCacheItem?.differenceStays[0]?.reason;

        // 价格计算
        priceCalcFun();
      } else {
        newSchemeList.value = oldSchemeList.value.map((e, idx) => {
          return {
            tempId: Date.now() + idx,
            miceDemandHotelId: e.miceDemandHotelId,
            tempSchemeHotelId: hotelList.value && hotelList.value.length === 1 ? hotelList.value[0].tempId : null,
            miceDemandPushHotelId:
              hotelList.value && hotelList.value.length === 1 ? hotelList.value[0].miceDemandPushHotelId : null,
            miceDemandStayId: e.id,
            demandDate: e.demandDate,
            roomType: e.roomType,
            breakfastType: e.breakfastType,
            personNum: e.personNum,
            schemeRoomNum: e.roomNum,
            discrepancyReason: e.discrepancyReason,
            description: e.description,

            schemeUnitPrice: null,
          };
        });
      }
    }

    if (oldSchemeList.value?.length > 0) {
      let td1 = 0;
      let td2 = '';
      let td3 = 0;
      let td4 = 0;
      let td5 = 0;

      oldSchemeList.value.forEach((e) => {
        td1 += e.personNum || 0;

        td2 =
          (td2 ? td2 + '，' : '') +
          (RoomTypeConstant.ofType(e.roomType)?.desc || '-') +
          '/' +
          (BreakfastTypeConstant.ofType(e.breakfastType)?.desc || '-') +
          '*' +
          (e.roomNum || 0);

        if (e.roomType === 1) {
          // 大床房（默认1人）
          td3 += e.roomNum;
        }
        if (e.roomType === 2) {
          // 双床房
          td4 += e.roomNum;
        }
        if (e.roomType === 3) {
          // 套房（默认1人）
          td5 += e.roomNum;
        }
      });

      // 需求差异
      demandTable.value = ['需求', td1, td2, td3, td4, td5];
    }
  }
});
</script>

<template>
  <!-- 住宿方案 -->
  <div class="scheme_stay">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="!['notBidding', 'biddingView'].includes(schemeType)">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>住宿需求</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '住宿' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ hotelName(item) }}
                </template>
                {{ hotelName(item) }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{
                    (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                    '/' +
                    (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                  }}
                </template>
                {{
                  (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                  '/' +
                  (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.personNum ? item.personNum + '人' : '-' }}
                </template>
                {{ item.personNum ? item.personNum + '人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.roomNum + '间夜' }}
                </template>
                {{ item.roomNum + '间夜' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.discrepancyReason || '-' }}
                </template>
                {{ item.discrepancyReason || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12">-</div>
          </div>
        </div>
      </div>

      <!-- 竞价 - 标的方案 -->
      <div class="common_table_l" v-else v-show="showBindingScheme">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>住宿方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '住宿' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr0">
              <a-select
                v-model:value="item.tempSchemeHotelId"
                :disabled="
                  (hotelList && hotelList.length === 1) ||
                  ['notBidding', 'biddingView', 'schemeView'].includes(schemeType)
                "
                placeholder="请选择酒店"
                :bordered="false"
                :dropdownMatchSelectWidth="300"
                allow-clear
              >
                <a-select-option v-for="(item, idx) in hotelList" :key="item.tempId" :value="item.tempId">
                  <a-tooltip placement="topLeft" :title="'酒店' + (idx + 1) + '-' + item.hotelName">
                    {{ '酒店' + (idx + 1) + '-' + item.hotelName }}
                  </a-tooltip>
                </a-select-option>
              </a-select>
            </div>
            <div class="scheme_plan_value pl12 pr0">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{
                    (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                    '/' +
                    (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                  }}
                </template>
                {{
                  (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                  '/' +
                  (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr0">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.personNum ? item.personNum + '人' : '-' }}
                </template>
                {{ item.personNum ? item.personNum + '人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr0">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeRoomNum + '间夜' }}
                </template>
                {{ item.schemeRoomNum + '间夜' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr0">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.discrepancyReason || '-' }}
                </template>
                {{ item.discrepancyReason || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr0">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div :class="['common_table_r', 'pr30']">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>住宿方案</span>
        </div>

        <div
          class="scheme_plan_table mt20"
          v-for="(item, idx) in newSchemeList"
          :key="idx"
          :id="'schemeStayId' + item.demandDate + idx"
        >
          <div class="scheme_plan_index">
            {{ '住宿' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'pr0', isVerifyFailed && !item.tempSchemeHotelId ? 'error_tip' : '']">
                <a-select
                  v-model:value="item.tempSchemeHotelId"
                  :disabled="
                    (hotelList && hotelList.length === 1) ||
                    ['notBidding', 'biddingView', 'schemeView', 'bidSchemeView'].includes(schemeType)
                  "
                  placeholder="请选择酒店"
                  :bordered="false"
                  :dropdownMatchSelectWidth="300"
                  allow-clear
                >
                  <a-select-option v-for="(item, idx) in hotelList" :key="item.tempId" :value="item.tempId">
                    <a-tooltip placement="topLeft" :title="'酒店' + (idx + 1) + '-' + item.hotelName">
                      {{ '酒店' + (idx + 1) + '-' + item.hotelName }}
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'pr0',
                  isVerifyFailed && (!item.roomType || item.breakfastType === undefined || item.breakfastType === null)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <a-tooltip
                  placement="topLeft"
                  v-if="['notBidding', 'biddingView', 'schemeView', 'bidSchemeView'].includes(schemeType)"
                >
                  <template #title>
                    {{
                      (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                      '/' +
                      (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                    }}
                  </template>
                  {{
                    (RoomTypeConstant.ofType(item.roomType)?.desc || '-') +
                    '/' +
                    (BreakfastTypeConstant.ofType(item.breakfastType)?.desc || '-')
                  }}
                </a-tooltip>

                <a-row v-else>
                  <a-col :span="14" style="border-right: 1px solid #e5e6eb">
                    <a-select
                      v-model:value="item.roomType"
                      @change="changeRoomNum(idx)"
                      placeholder="房型"
                      :bordered="false"
                      :dropdownMatchSelectWidth="160"
                      allow-clear
                    >
                      <a-select-option v-for="item in RoomTypeConstant.toArray()" :key="item.code" :value="item.code">
                        <a-tooltip placement="topLeft" :title="item.desc">
                          {{ item.desc }}
                        </a-tooltip>
                      </a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="10" class="pl5">
                    <a-select
                      v-model:value="item.breakfastType"
                      placeholder="早餐"
                      :bordered="false"
                      :dropdownMatchSelectWidth="90"
                      allow-clear
                    >
                      <a-select-option
                        v-for="item in BreakfastTypeConstant.toArray()"
                        :key="item.code"
                        :value="item.code"
                      >
                        <a-tooltip placement="topLeft" :title="item.desc">
                          {{ item.desc }}
                        </a-tooltip>
                      </a-select-option>
                    </a-select>
                  </a-col>
                </a-row>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'pr0', isVerifyFailed && !item.personNum ? 'error_tip' : '']">
                <a-tooltip
                  placement="topLeft"
                  v-if="['notBidding', 'biddingView', 'schemeView', 'bidSchemeView'].includes(schemeType)"
                >
                  <template #title>
                    {{ item.personNum ? item.personNum + '人' : '-' }}
                  </template>
                  {{ item.personNum ? item.personNum + '人' : '-' }}
                </a-tooltip>
                <div v-else>
                  <a-input-number
                    style="width: calc(100% - 44px)"
                    v-model:value="item.personNum"
                    @blur="changeRoomNum(idx)"
                    placeholder="住宿人数"
                    :bordered="false"
                    allow-clear
                    :min="1"
                    :max="99999"
                  />
                  <span>人</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div :class="['scheme_plan_border', 'pr0', isVerifyFailed && !item.schemeRoomNum ? 'error_tip' : '']">
                <a-tooltip
                  placement="topLeft"
                  v-if="['notBidding', 'biddingView', 'schemeView', 'bidSchemeView'].includes(schemeType)"
                >
                  <template #title>
                    {{ item.schemeRoomNum + '间夜' }}
                  </template>
                  {{ item.schemeRoomNum + '间夜' }}
                </a-tooltip>
                <div v-else>
                  <a-input-number
                    style="width: calc(100% - 58px)"
                    v-model:value="item.schemeRoomNum"
                    placeholder="房间数"
                    :bordered="false"
                    allow-clear
                    :min="1"
                    :max="99999"
                  />
                  <span>间夜</span>
                  <div class="scheme_plan_edit"></div>
                </div>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'pr0',
                  isVerifyFailed &&
                  !item.discrepancyReason &&
                  Number(item.personNum) !==
                    Number(item.schemeRoomNum) * (item.roomType === 1 || item.roomType === 3 ? 1 : 2) &&
                  !item.discrepancyReason
                    ? 'error_tip'
                    : '',
                ]"
              >
                <a-tooltip
                  placement="topLeft"
                  v-if="['notBidding', 'biddingView', 'schemeView', 'bidSchemeView'].includes(schemeType)"
                >
                  <template #title>
                    {{ item.discrepancyReason || '-' }}
                  </template>
                  {{ item.discrepancyReason || '-' }}
                </a-tooltip>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.discrepancyReason">
                    {{ item.discrepancyReason || '-' }}
                  </template>
                  <a-input
                    v-model:value="item.discrepancyReason"
                    :disabled="
                      Number(item.personNum) ===
                      Number(item.schemeRoomNum) * (item.roomType === 1 || item.roomType === 3 ? 1 : 2)
                    "
                    style="width: calc(100% - 30px)"
                    placeholder="不一致原因"
                    :maxlength="500"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div class="scheme_plan_border pr0">
                <a-tooltip
                  class="pr12"
                  placement="topLeft"
                  v-if="['notBidding', 'biddingView', 'schemeView', 'bidSchemeView'].includes(schemeType)"
                >
                  <template #title>
                    {{ item.description || '-' }}
                  </template>
                  {{ item.description || '-' }}
                </a-tooltip>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.description">
                    {{ item.description || '-' }}
                  </template>
                  <a-input
                    v-model:value="item.description"
                    style="width: calc(100% - 30px)"
                    placeholder="备注"
                    :maxlength="500"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">单价：</div>
              <div
                class="scheme_plan_price_value"
                v-if="['biddingView', 'schemeView', 'bidSchemeView'].includes(schemeType)"
              >
                {{ '¥' + formatNumberThousands(item.schemeUnitPrice) }}
              </div>
              <div
                :class="['scheme_plan_price_value', isVerifyFailed && !item.schemeUnitPrice ? 'error_price_tip' : '']"
                v-else
              >
                <a-input-number
                  v-model:value="item.schemeUnitPrice"
                  @change="changePrice(idx)"
                  placeholder=""
                  :bordered="false"
                  :controls="false"
                  :min="0.01"
                  :max="999999.99"
                  :precision="2"
                  style="width: 100%"
                  allow-clear
                />
              </div>
            </div>
            <div class="scheme_plan_price mt36">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  '¥' +
                  (item.schemeRoomNum && item.schemeUnitPrice
                    ? formatNumberThousands(item.schemeRoomNum * item.schemeUnitPrice)
                    : '0.00')
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <span v-if="item.schemeRoomNum && item.schemeUnitPrice">
                {{ item.schemeRoomNum + '(间夜)*' + item.schemeUnitPrice + '(单价)' }}
              </span>
            </div>

            <!-- 操作 -->
            <div class="action_icons" v-if="processNode === 'SCHEME_SUBMIT' && newSchemeList.length > 1">
              <a-popconfirm
                :title="'确认删除住宿' + (idx + 1) + '？'"
                placement="topRight"
                ok-text="确认"
                cancel-text="取消"
                @confirm="delScheme(idx)"
              >
                <div class="del_icon"></div>
              </a-popconfirm>
            </div>
          </div>
        </div>

        <div v-show="newSchemeList.length > 0" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>

        <div v-if="processNode === 'SCHEME_SUBMIT'" class="add_scheme_plan mt20" @click="addScheme(index)">
          <div class="plan_add_img mr8"></div>
          <span>新增住宿</span>
        </div>
      </div>
    </div>

    <!-- 差异 -->
    <div v-if="isShowDiff && !['notBidding', 'biddingView'].includes(schemeType)" class="scheme_stay_difference">
      <div class="stay_difference_border">
        <div class="stay_difference_title">差异：</div>

        <a-row class="stay_difference_content" :gutter="48">
          <a-col :span="16">
            <div class="stay_difference_table">
              <div class="stay_difference_tr">
                <div class="stay_difference_td" v-for="(difItem, index) in stayDifferenceTable" :key="index">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ difItem }}
                    </template>
                    {{ difItem }}
                  </a-tooltip>
                </div>
              </div>
              <div class="stay_difference_tr">
                <div class="stay_difference_td" v-for="(difItem2, index) in demandTable" :key="index">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ difItem2 }}
                    </template>
                    {{ difItem2 }}
                  </a-tooltip>
                </div>
              </div>
              <div class="stay_difference_tr">
                <div
                  v-for="(difItem3, index) in planTable"
                  :key="index"
                  :class="['stay_difference_td', diffColor[index] ? '' : 'diff_color']"
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ difItem3 }}
                    </template>
                    {{ difItem3 }}
                  </a-tooltip>
                </div>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="stay_difference_reason">
              <div class="stay_difference_reason_title">差异原因：</div>
              <div class="stay_difference_reason_text">
                <a-textarea
                  :id="'schemeStayDiffId' + newSchemeList[0].demandDate"
                  v-model:value="diffRemarks"
                  :disabled="['notBidding', 'biddingView', 'schemeView', 'bidSchemeView'].includes(schemeType)"
                  placeholder="请输入原因"
                  :maxlength="500"
                  :autoSize="{ minRows: 3, maxRows: 3 }"
                />
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_stay {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_stay.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  :deep(.ant-select) {
    /* 下拉框 */
    width: 100%;
    height: 100%;
  }
  :deep(.ant-select-selector) {
    padding: 0;
  }

  :deep(.ant-select-arrow) {
    /* 下拉箭头 */
    margin-top: -8px;
  }
  :deep(.ant-select .ant-select-clear) {
    /* 清除图标 */
    margin-top: -8px;
  }
  :deep(.ant-input-affix-wrapper) {
    padding-left: 0;
    padding-right: 0;
  }

  .scheme_plan_value {
    /* 住宿人数 */
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .border_err {
    border-color: red !important;
  }

  .scheme_plan_price_value {
    /* 竞价单价 */
    :deep(.ant-input-number .ant-input-number-input) {
      /* input数字输入框 */
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }
  .error_price_tip {
    :deep(.ant-input-number .ant-input-number-input) {
      border-bottom: 2px solid #ff4d4f;
    }
  }

  .scheme_plan_list3 {
  }

  .pr0 {
    padding-right: 0 !important;
  }
  .pr30 {
    padding-right: 30px !important;
  }

  .scheme_stay_difference {
    height: 202px;
    background: #ffffff;
    border: 1px solid #e5e6eb;
    border-top: none;

    .stay_difference_border {
      padding: 16px 24px;
      width: 100%;
      height: 100%;
      border: 8px solid rgba(255, 85, 51, 0.2);
    }

    .stay_difference_title {
      font-weight: 500;
      font-size: 18px;
      color: #4e5969;
      line-height: 25px;
    }

    .stay_difference_content {
      .stay_difference_table {
        .stay_difference_tr {
          border-bottom: 1px solid #e5e6eb;
          display: flex;
          justify-content: space-between;
        }
        .stay_difference_td {
          width: 90px;
          height: 44px;
          color: #86909c;
          line-height: 44px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;

          &:first-child {
            width: 80px;
          }
          &:nth-child(3) {
            min-width: 160px;
            width: calc(100% - 440px - 30px);
          }
        }

        .diff_color {
          color: #ff5533;
        }
      }

      .stay_difference_reason {
        .stay_difference_reason_title {
          height: 44px;
          color: #4e5969;
          line-height: 44px;
        }
        .stay_difference_reason_text {
          :deep(.ant-input) {
            height: 88px !important;
            min-height: 88px !important;
            max-height: 88px !important;
          }
        }
      }
    }
  }
  /* custom-antd.css */
  :deep(.ant-select-disabled .ant-select-selector) {
    color: rgba(134, 144, 156, 1);
  }
}
</style>

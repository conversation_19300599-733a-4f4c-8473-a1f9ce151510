<script lang="ts" setup>
import {
  Button as hButton,
  Table as hTable,
  Textarea as hTextarea,
  message,
  FormItem as hFormItem,
  Upload as hUpload,
  Modal as hModal,
  Col as hCol,
  Row as hRow,
} from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { computed, ref, watch, onMounted, nextTick, h, Ref } from 'vue';
import { fileApi, miceBidManOrderListApi, miceBidManOrderLogListApi } from '@haierbusiness-front/apis';
import { ColumnType } from 'ant-design-vue/es/table';
import { getFileNameFromPath, resolveParam } from '@haierbusiness-front/utils';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { MiceBidManMemorandum, UploadFiles } from '@haierbusiness-front/common-libs';
const store = applicationStore();
const { loginUser } = storeToRefs(store);

const memorandumDetails = ref<MiceBidManMemorandum>()
const props = defineProps({
  nodeId: {
    type: String,
  },
});

// type：主流程日志传0 分配日志传1
// systemType：用户端1 平台端2 商户端 4
//备忘录
const memorandumList = ref({})

const loading = ref(false);
const mainProcessLog = async (pageNum, pageSize) => {
  loading.value = true;
  try {
    const res = await miceBidManOrderLogListApi.memorandum({
      mainCode: mainCode.value,
      pageNum,
      pageSize
    });
    memorandumList.value = res; // 确保默认值
    console.log(memorandumList.value, "memorandumList.value");

  } catch (error) {
    console.error('日志获取失败:', error);
    // 可添加错误提示 toast.error('获取失败')
  } finally {
    loading.value = false;
  }
}

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  mainProcessLog(pag.current, pag.pageSize);
};

const orderDetail = async () => {
  const nodeId = resolveParam(props.nodeId).miceId
  const res = await miceBidManOrderListApi.platformDetails({
    miceId: nodeId,
  });
  mainCode.value = res.mainCode
}


onMounted(async () => {
  await orderDetail()
  await mainProcessLog(1, 10)

})

const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '60px',
    align: 'center',
    customRender: ({ index }) => index + 1,

  },
  {
    title: '内容',
    dataIndex: 'content',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },

  {
    title: '完成说明',
    dataIndex: 'completeExplain',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '140px',
    ellipsis: true,
    fixed: 'right',
    align: 'center',
  },
];

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: memorandumList.value?.total,
  current: memorandumList.value?.pageNum,
  pageSize: memorandumList.value?.pageSize,
  style: { justifyContent: 'center' },
}));

const visible = ref<boolean>(false);
const isfinish = ref<boolean>(false)
const confirmLoading = ref(false);
const mainCode = ref('')
const title = ref('')
const from = ref();
const add = () => {
  title.value = '备忘录新增'
  from.value?.resetFields()
  memorandumDetails.value = {}
  isfinish.value = false;
  fileList.value = []
  visible.value = true
}
const edit = (id: number) => {
  from.value?.clearValidate()
  details(id)
  title.value = '备忘录编辑';
  isfinish.value = false;
}
const view = async (id: number) => {
  from.value?.clearValidate()
  await details(id)
  title.value = '备忘录查看'
  isfinish.value = true
}

const deleted = (id: number) => {
  hModal.confirm({
    title: '确认要删除吗？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      miceBidManOrderLogListApi.delete(id)
        .then(() => {
          message.success('删除成功')
          mainProcessLog(1, 10)
        })
        .catch(() => {
          message.success('删除失败')
        })
    },
    onCancel() {
      console.log('Cancel');
    },
  });
}
const finish = async (id: number) => {
  from.value?.clearValidate()
  await details(id)
  title.value = '备忘录完成'
  isfinish.value = true
}

const details = async (id: number) => {
  try {
    const res = await miceBidManOrderLogListApi.details(id);
    console.log(res, "memorandumDetails.value");
    memorandumDetails.value = res
    if (res && memorandumDetails.value) {
      // 回填附件信息
      console.log(res, "res");
      if (res.attachmentPaths && res.attachmentPaths.length > 0) {
        fileList.value = res.attachmentPaths.map((file, index: number) => ({
          uid: `-${index}`,
          name: file.path?.split('/').pop()?.split('-').slice(1).join('-') || '文件1',
          status: 'done',
          url: file.path,
          filePath: file.path
        }));
      } else {
        fileList.value = []
      }
    }
    visible.value = true;
  } catch (error) {
    console.error('获取详情失败:', error);
    // 可以添加错误提示 toast.error('获取详情失败')
    visible.value = false;
  }
}

const handleOk = async () => {
  const nodeId = resolveParam(props.nodeId).miceId;
  confirmLoading.value = true;

  try {
    await from.value.validate();

    if (title.value === '备忘录新增') {
      await handleAdd(nodeId);
    } else if (title.value === '备忘录编辑') {
      await handleEdit();
    } else if (title.value === '备忘录完成') {
      await handleComplete();
    }
  } catch (error) {
    handleError(error);
  } finally {
    confirmLoading.value = false;
  }
};

const handleAdd = async (nodeId) => {
  if (fileList.value?.length) {
    memorandumDetails.value.attachmentPaths = fileList.value.map(file => ({
      type: 31,
      path: file.filePath
    }));
  }

  const params = {
    mainCode: mainCode.value,
    miceId: nodeId,
    ...memorandumDetails.value
  };

  try {
    await miceBidManOrderLogListApi.add(params);
    message.success('新增成功');
    refreshView();
  } catch {
    message.error('新增失败');
    throw new Error('Add failed');
  }
};

const handleEdit = async () => {
  let attachments = [];
  if (fileList.value?.length) {
    attachments = fileList.value.map(file => ({
      type: 31,
      path: file.filePath
    }));
  }

  const params = {
    id: memorandumDetails.value.id,
    mainCode: memorandumDetails.value.mainCode,
    miceId: memorandumDetails.value.miceId,
    content: memorandumDetails.value.content,
    isHandled: memorandumDetails.value.isHandled,
    attachments: memorandumDetails.value.attachmentPaths?.length
      ? memorandumDetails.value.attachmentPaths.map((item, index) => ({
        ...item,
        path: attachments[index]?.path || item.path
      }))
      : attachments
  };

  try {
    await miceBidManOrderLogListApi.edit(params);
    message.success('编辑成功');
    refreshView();
  } catch {
    message.error('编辑失败');
    throw new Error('Edit failed');
  }
};

const handleComplete = async () => {
  const params = {
    id: Number(memorandumDetails.value?.id),
    completeExplain: memorandumDetails.value?.completeExplain
  };

  try {
    await miceBidManOrderLogListApi.treated(params)
      .then(() => {
        message.success('完成成功');
        refreshView();
      })
      .catch(() => {
        message.error('完成失败');
      })

  } catch {
    message.error('完成失败');
    throw new Error('Complete failed');
  }
};

const refreshView = () => {
  mainProcessLog(1, 10);
  visible.value = false;
};

const handleError = (error) => {
  const action = memorandumDetails.value.id ? '编辑' : '新增';
  message.error(`${action}失败`);
  console.error('操作失败:', error);
};




const rules = {
  content: [
    { required: true, message: '请输入内容', trigger: 'change' },
  ],
  completeExplain: [
    { required: true, message: '请输入完成说明', trigger: 'change' },
  ]
};

// 文件列表
const fileList = ref<UploadFiles[]>([]);
// 基础URL
const baseUrl = import.meta.env?.VITE_BUSINESS_URL || '';

// 上传文件相关
const uploadRequest = (options: any) => {
  loading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((response) => {
      loading.value = false;
      if (response && response.path) {
        const fullPath = baseUrl + response.path;
        options.file.filePath = fullPath;
        options.file.fileName = options.file.name;
        options.file.url = fullPath; // 设置URL以便预览
        options.file.thumbUrl = fullPath; // 设置缩略图URL
        options.onSuccess(response, options.file);
      } else {
        options.onError('上传失败');
        message.error('上传失败，请重试');
      }
    })
    .catch((error) => {
      loading.value = false;
      options.onError('上传失败');
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    });
};

// 移除文件
const handleRemove = (file: UploadFiles) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
};



</script>

<template>
  <div style="margin-top: 20px;">
    <h-button type="primary" @click="add">新增</h-button>
  </div>
  <h-table :columns="columns" :data-source="memorandumList.records" :pagination="pagination" :loading="loading"
    @change="handleTableChange($event as any)" style="margin-top: 10px;" bordered :scroll="{ x: 600, y: 300 }" size="middle">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === '_operator'">
        <h-button type="link" @click="edit(record.id)" v-if="record.isHandled == false">编辑</h-button>
        <h-button type="link" @click="view(record.id)" v-if="record.isHandled != false">查看</h-button>
        <h-button type="link" @click="deleted(record.id)" v-if="record.isHandled == false">删除</h-button>
        <h-button type="link" @click="finish(record.id)" v-if="record.isHandled == false">完成</h-button>
      </template>
    </template>
  </h-table>
  <h-modal :visible="visible" width="40%" :confirmLoading="confirmLoading" @cancel="visible = false" :title="title">
    <a-form ref="from" :model="memorandumDetails" :label-col="{ span: 4 }" :rules="rules" :hide-required-mark="true">
      <h-form-item label="内容：" name="content" v-if="!isfinish">
        <h-textarea v-model:value="memorandumDetails.content" placeholder="请输入内容，最多200字" :maxlength="500" show-count style="min-height: 200px;"/>
      </h-form-item>
      <h-form-item label="附件：" v-if="!isfinish">
        <h-upload v-model:fileList="fileList" :custom-request="uploadRequest" :multiple="false" :max-count="1"
          @remove="handleRemove" :show-upload-list="true">
          <h-button>
            <upload-outlined></upload-outlined>
            上传附件
          </h-button>
        </h-upload>
      </h-form-item>
      <!-- <h-form-item label="内容：" v-if="isfinish">
        
      </h-form-item> -->
      <div style="width: 100%;" v-if="isfinish">
        <h-row style="width: 100%;display: block;display: flex">
          <h-col span="4" style="text-align: right;">内容：</h-col>
          <h-col span="18">{{ memorandumDetails?.content }}</h-col>
        </h-row>
      </div>

      <h-form-item label="附件：" v-if="isfinish" style="margin-top: 15px;">
        <p v-if="memorandumDetails.attachmentPaths.length > 0">
          <a :href="memorandumDetails.attachmentPaths[0].path" rel="noopener" target="_blank">{{
            memorandumDetails.attachmentPaths[0].path?.split('/').pop()?.split('-').slice(1).join('-') }}</a>
        </p>
        <p v-else>无</p>
      </h-form-item>

      <h-form-item label="完成说明：" v-if="isfinish && title == '备忘录完成'" name="completeExplain">
        <h-textarea v-model:value="memorandumDetails.completeExplain" placeholder="请输入完成说明，最多200字" :maxlength="200"
          show-count />
      </h-form-item>
      <div style="width: 100%;" v-if="title == '备忘录查看'">
        <h-row style="width: 100%;display: block;display: flex">
          <h-col span="4" style="text-align: right;">完成说明：</h-col>
          <h-col span="18">{{ memorandumDetails?.completeExplain }}</h-col>
        </h-row>
      </div>
    </a-form>
    <template #footer>
      <a-button key="back" @click="visible = false">关闭</a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="handleOk" v-if="title !== '备忘录查看'">确定</a-button>
    </template>
  </h-modal>
</template>

<style lang="less" scoped>
.important {
  color: red;
}

.ant-input-textarea-show-count::after {
  position: absolute;
  right: 12px;
  bottom: 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

P {
  margin-bottom: 0px;
}
:deep(.ant-table-tbody>tr>td){
  padding: 10px 8px;
}
</style>
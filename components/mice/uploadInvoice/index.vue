<!-- 上传发票页面 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  InputNumber as hInputNumber,
  DatePicker as hDatePicker,
  Card,
  message,
  Modal,
  Table as hTable,
  Popconfirm,
} from 'ant-design-vue';
import { ArrowLeftOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { paymentFromApi } from '@haierbusiness-front/apis';
import { UploadGLInvoiceParams } from '@haierbusiness-front/common-libs';
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// 接收 props 参数
interface Props {
  type?: 'payment' | 'manage';  // payment: 付款场景, manage: 缴费场景
}

const props = withDefaults(defineProps<Props>(), {
  type: 'manage'
});

const route = useRoute();
const currentRouter = useRouter();

// 页面数据
const loading = ref(false);
const paymentRecord = ref<any>(null);
const formRef = ref<any>(null);

// 发票列表数据
const invoiceList = ref<Array<{
  id: string;
  invoiceDate: string;
  invoiceNumber: string;
  invoiceAmount: number | undefined;
  editing?: boolean;
}>>([]);

// 审批流相关
const approveCode = ref<string>('');
const approvalModalShow = ref<boolean>(false);
const businessProcess = (import.meta as any).env?.VITE_BUSINESS_PROCESS_URL || '';

// 发票类型字段已移除（选填字段不传）

// 页面初始化
onMounted(() => {
  const record = route.query.record;
  if (record && typeof record === 'string') {
    try {
      paymentRecord.value = JSON.parse(decodeURIComponent(record));
      // 初始化发票列表
      initInvoiceList();
    } catch (error) {
      console.error('解析记录数据失败:', error);
      message.error('页面参数错误');
      goBack();
    }
  } else {
    message.error('缺少必要参数');
    goBack();
  }
});

// 返回列表页
const goBack = () => {
  if (props.type === 'payment') {
    // 付款场景，返回付款单列表
    currentRouter.push({
      path: '/mice-merchant/paymentDocument/paymentOrderList',
    });
  } else {
    // 缴费场景，返回缴费单列表  
    currentRouter.push({
      name: 'PaymentBill',
    });
  }
};

// 生成唯一ID
const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// 新增发票
const addInvoice = () => {
  invoiceList.value.push({
    id: generateId(),
    invoiceDate: '',
    invoiceNumber: '',
    invoiceAmount: undefined,
    editing: true,
  });
};

// 删除发票
const removeInvoice = (id: string) => {
  const index = invoiceList.value.findIndex(item => item.id === id);
  if (index > -1) {
    invoiceList.value.splice(index, 1);
  }
};

// 表格列定义已移除，使用自定义表格布局

// 验证发票数据
const validateInvoices = () => {
  if (invoiceList.value.length === 0) {
    message.error('请至少添加一条发票信息');
    return false;
  }

  for (const invoice of invoiceList.value) {
    if (!invoice.invoiceDate) {
      message.error('请选择发票日期');
      return false;
    }
    if (!invoice.invoiceNumber) {
      message.error('请输入发票号');
      return false;
    }
    if (!invoice.invoiceAmount || invoice.invoiceAmount <= 0) {
      message.error('请输入有效的发票金额');
      return false;
    }
  }
  return true;
};

// 提交上传发票
const submitInvoice = () => {
  if (!validateInvoices()) {
    return;
  }

  if (!paymentRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  loading.value = true;

  // 构建批量上传的数据格式
  const batchInvoiceData: UploadGLInvoiceParams = {
    paymentId: paymentRecord.value.id,
    invoiceList: invoiceList.value.map(invoice => ({
      paymentCode: paymentRecord.value.receivePaymentCode || paymentRecord.value.paymentCode,
      invoiceDate: invoice.invoiceDate,
      invoiceNumber: invoice.invoiceNumber,
      invoiceAmount: invoice.invoiceAmount || 0,
      // invoiceType: invoice.invoiceType, // 可选字段，如需要可取消注释
    }))
  };

  // 根据type调用不同的API接口进行批量上传
  const uploadPromise = props.type === 'payment' 
    ? paymentFromApi.uploadPaymentInvoice(batchInvoiceData)
    : paymentFromApi.uploadInvoice(batchInvoiceData);

  uploadPromise
    .then((result) => {
      message.success(`发票上传成功，共上传 ${invoiceList.value.length} 条发票信息`);
      
      // 获取审批代码
      if (result) {
        const approvalCode = String(result);
        // 显示审批流弹窗
        approvalModalShow.value = true;
        // 审批Code赋值
        approveCode.value = approvalCode;
      }
    })
    .catch((error) => {
      console.error('上传发票失败:', error);
      message.error('上传发票失败，请重试');
    })
    .finally(() => {
      loading.value = false;
    });
};

// 关闭审批弹窗并返回列表
const closeApproval = () => {
  approvalModalShow.value = false;
  approveCode.value = '';
  goBack();
};

// 重置表单
const resetForm = () => {
  invoiceList.value = [];
};

// 页面初始化时添加一条空的发票记录
const initInvoiceList = () => {
  if (invoiceList.value.length === 0) {
    addInvoice();
  }
};
</script>

<template>
  <div class="page-container">
    <!-- 主要内容区域 -->
    <div class="content-wrapper">
      <div class="payment-info-card">
        <div class="payment-info-content">
          <!-- 主标题：服务商名称 -->
          <div class="payment-main-title">
            <div class="payment-main-icon"></div>
            <span class="payment-main-name">{{ paymentRecord?.merchantName || '未知服务商' }}</span>
          </div>
          
          <!-- 收款单号 -->
          <div class="payment-code">
            <span>收款单号：{{ paymentRecord?.receivePaymentCode || '-' }}</span>
          </div>
          
          <!-- 详细信息 -->
          <div class="payment-info-details">
            <div class="payment-info-item">
              <span class="info-title amount-icon">账单总金额：</span>
              <span class="info-value">{{ paymentRecord?.totalAmount ? `${paymentRecord.totalAmount.toFixed(2)}元` : '-' }}</span>
            </div>
            <div class="payment-info-item">
              <span class="info-title payment-icon">收款金额：</span>
              <span class="info-value">{{ paymentRecord?.receivePaymentAmount ? `${paymentRecord.receivePaymentAmount.toFixed(2)}元` : '-' }}</span>
            </div>
          </div>
        </div>
      </div>

      <Card title="发票信息" class="form-card">
        <!-- 发票表格 -->
        <div class="info-table-wrapper invoice-table">
          <div class="table-header">
            <div class="col-serial font-color">序号</div>
            <div class="col-date font-color">发票日期</div>
            <div class="col-number font-color">发票号</div>
            <div class="col-amount font-color">发票金额（元）</div>
            <div class="col-operation font-color">操作</div>
          </div>
          <div class="table-body">
            <div
              v-for="(item, index) in invoiceList"
              :key="item.id"
              class="table-row"
            >
              <!-- 序号 -->
              <div class="col-serial">
                {{ index + 1 }}
              </div>

              <!-- 发票日期 -->
              <div class="col-date">
                <h-date-picker
                  v-model:value="item.invoiceDate"
                  placeholder="请选择发票日期"
                  value-format="YYYY-MM-DD"
                  size="small"
                  class="borderless-input"
                  :bordered="false"
                  allowClear
                />
              </div>

              <!-- 发票号 -->
              <div class="col-number">
                <h-input
                  v-model:value="item.invoiceNumber"
                  placeholder="请输入发票号"
                  maxLength="200"
                  size="small"
                  class="borderless-input"
                  :bordered="false"
                  allowClear
                />
              </div>

              <!-- 发票金额 -->
              <div class="col-amount">
                <h-input-number
                  v-model:value="item.invoiceAmount"
                  placeholder="请输入发票金额"
                  :min="0"
                  :precision="2"
                  size="small"
                  class="borderless-input"
                  :bordered="false"
                  :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                  :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                />
              </div>

              <!-- 操作 -->
              <div class="col-operation">
                <Popconfirm
                  title="确定删除这条发票信息吗？"
                  @confirm="removeInvoice(item.id)"
                  okText="确定"
                  cancelText="取消"
                >
                  <h-button type="link" danger size="small">
                    <DeleteOutlined />
                  </h-button>
                </Popconfirm>
              </div>
            </div>

            <!-- 添加按钮行 -->
            <div class="table-row add-row">
              <div class="add-button-full-width" @click="addInvoice">
                <div class="demand_add">
                  <PlusOutlined style="margin-right: 5px" />
                  <span>新增发票</span>
                </div>
              </div>
            </div>
          </div>
        </div>

      </Card>
    </div>

    <!-- 底部操作按钮 -->
    <div class="footer-container">
      <h-button @click="resetForm" style="margin-right: 10px">重置</h-button>
      <h-button type="primary" @click="submitInvoice" :loading="loading">
        提交
      </h-button>
    </div>

    <!-- 审批流弹窗 -->
    <Modal
      v-model:open="approvalModalShow"
      title="审批流程"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
      width="80%"
    >
      <div>
        <iframe 
          width="100%" 
          height="600px"
          :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" 
          frameborder="0"
        ></iframe>
      </div>
      <template #footer>
        <h-button @click="closeApproval">确定</h-button>
      </template>
    </Modal>
  </div>
</template>

<style scoped lang="less">
// 页面容器
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100%;
  background-color: #f5f5f5;
  width: 100%;
  position: relative;
}

// 页面标题栏
.page-header {
  background-color: #ffffff;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 16px;
}

.back-button {
  margin-right: 12px;
  color: #1890ff;
  
  &:hover {
    color: #40a9ff;
  }
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  color: #262626;
}

// 主要内容区域
.content-wrapper {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 60px;
  display: flex;
  flex-direction: column;
  /* 给底部按钮留出空间 */
}

// 缴费单信息卡片
.payment-info-card {
  flex-shrink: 0;
}

.payment-info-content {
  padding: 24px 32px;
  width: 100%;
  height: 100%;
  background: url('@/assets/image/scheme/mice_bgc.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 6px;
}

.payment-main-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.payment-main-icon {
  width: 28px;
  height: 28px;
  background-image: url('@/assets/image/scheme/mice_name.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-right: 12px;
}

.payment-main-name {
  font-family: PingFangSCSemibold, PingFangSCSemibold;
  font-weight: normal;
  font-size: 20px;
  color: #1d2129;
  line-height: 28px;
}

.payment-code {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
  color: #86909c;
  line-height: 20px;
}

.payment-info-details {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.payment-info-item {
  width: 48%;
  font-size: 14px;
  color: #86909c;
  line-height: 20px;
}

.info-title {
  display: inline-block;
  text-indent: 26px;
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center left;
  margin-right: 8px;
  
  &.amount-icon {
    background-image: url('@/assets/image/scheme/mice_type.png');
  }
  
  &.payment-icon {
    background-image: url('@/assets/image/scheme/mice_person.png');
  }
}

.info-value {
  color: #1d2129;
}

// 表单卡片
.form-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  :deep(.ant-card-head-title) {
    font-weight: 500;
  }
  
  :deep(.ant-card-body) {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }
}

// 表格样式
.info-table-wrapper {
  width: 100%;
  border: none;
  border-bottom: 1px solid #d9d9d9;
  border-radius: 0;
  margin-bottom: 0;
  padding: 0px;

  &.invoice-table {
    width: 100%;
  }

  .table-header {
    display: flex;
    background-color: #f2f3f5;
    font-weight: 500;
    font-size: 14px;
    color: #333;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
      sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    > div {
      padding: 12px 8px;
      text-align: center;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .font-color {
      color: #86909c;
    }

    .col-serial {
      width: 80px;
    }

    .col-date {
      width: 200px;
    }

    .col-number {
      width: 250px;
    }

    .col-amount {
      width: 200px;
    }

    .col-operation {
      width: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1px solid #e5e6eb;

      > div {
        padding: 12px 8px;
        display: flex;
        align-items: center;
        min-height: 38px;
        justify-content: center;

        &.col-operation {
          justify-content: center;
          align-items: center;
          padding: 8px;
        }
      }

      &:last-child {
        border-bottom: none;
      }

      &.add-row {
        border-bottom: none;
        margin-top: 5px;

        .add-button-full-width {
          width: auto;
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          min-height: 38px;
          cursor: pointer;
          border: none;
          border-radius: 6px;
          background-color: transparent;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f5f5f5;
          }

          .demand_add {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            color: #1890ff;
            font-size: 14px;
            margin-left: 0;
          }
        }
      }

      .col-serial {
        width: 80px;
      }

      .col-date {
        width: 200px;
      }

      .col-number {
        width: 250px;
      }

      .col-amount {
        width: 200px;
      }

      .col-operation {
        width: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 8px;

        .ant-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 4px;
          height: 32px;
          width: 32px;
          min-width: 32px;
          border-radius: 4px;

          :deep(.anticon) {
            font-size: 16px;
            line-height: 1;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .ant-popconfirm {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

// 无边框输入框样式
.borderless-input {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  width: 100%;

  &:focus,
  &:hover {
    border: none !important;
    box-shadow: none !important;
  }

  .ant-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
  }

  .ant-picker-input > input {
    border: none !important;
    box-shadow: none !important;
  }
}

// 底部按钮容器
.footer-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 220px;
  right: 0;
  z-index: 1000;
}

// 响应式设计
@media (max-width: 768px) {
  .content-wrapper {
    padding: 12px 0;
  }
  
  .payment-info-card {
    margin: 0 16px 16px 16px;
  }
  
  .form-card {
    margin: 0 16px;
  }
  
  .page-header {
    padding: 12px 16px;
  }
  
  .footer-container {
    padding: 8px 16px;
  }
  
  .payment-info-content {
    padding: 16px 20px;
  }
  
  .payment-info-details {
    flex-direction: column;
    gap: 12px;
  }
  
  .payment-info-item {
    width: 100%;
  }
}

// Form样式优化
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-input), :deep(.ant-input-number), :deep(.ant-picker) {
  border-radius: 6px;
}

// Card样式优化
:deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #e8e8e8;
}
</style>

<script setup lang="ts">
// 方案互动-补充条目（可编辑版本）
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';
import arrow from '@/assets/image/orderList/delete.png'; //删除
import deleteGray from '@/assets/image/orderList/deleteGray.png'; //删除灰色
import add from '@/assets/image/orderList/add.png'; //添加
import dayjs, { Dayjs } from 'dayjs';
import { UploadFile, SupplementTypeConstant } from '@haierbusiness-front/common-libs';
//鼠标移入效果
const hoverColor = ref(-1)
// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
// 类型定义
interface SupplementItem {
  key: string;
  tempId: string; // 🔧 新增：临时ID，用于关联匹配
  serialNumber: number;
  occurDate: Dayjs | string | null; // 发生时间
  itemName: string; // 项目名称
  type: number | null; // 类型：客损/其他等
  billNum: number | null; // 账单数量
  billUnitPrice: number | null; // 账单单价
  description: string; // 描述
  attachments: string[]; // 补充条目附件（字符串数组）
  attachmentFiles: UploadFile[]; // 用于UI显示的文件对象
  invoiceTempId?: string | null; // 🔧 新增：关联的发票临时ID
  statementTempId?: string | null; // 🔧 新增：关联的水单临时ID
}

const props = defineProps({
  miceId: {
    type: Number,
    default: null,
  },
  schemeDetail: {
    type: Object,
    default: () => ({}),
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  invoiceTempId: {
    // 临时id, 用于关联发票表
    type: Number,
    default: null,
  },
  statementTempId: {
    // 临时id, 用于关联水单表
    type: Number,
    default: null,
  },
  additionalItems: {
    // 临时id, 用于关联水单表
    type: Object,
    default: {},
  },
});

const emit = defineEmits(['supplementEntryEmit']);

// 文件上传相关常量
const SUPPORTED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const FILE_SIZE_LIMIT = 10; // MB
const UPLOAD_ACCEPT = '.pdf,.jpg,.jpeg,.png,.gif,.doc,.docx';

// 响应式数据
const uploadLoadingMap = ref<Record<string, boolean>>({});
const previewVisible = ref(false);

// 锚点跳转函数
const anchorJump = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};
const previewFile = ref<UploadFile | null>(null);
const previewFileName = ref('');

// 表格数据 - 改为可编辑的空数据
const dataSource = ref<SupplementItem[]>([]);

// 类型选项 - 使用SupplementTypeConstant枚举
const typeOptions = [
  { value: SupplementTypeConstant.CUSTOMER_LOSSES.code, label: SupplementTypeConstant.CUSTOMER_LOSSES.desc },
  { value: SupplementTypeConstant.OTHER.code, label: SupplementTypeConstant.OTHER.desc },
];

// 初始化标志，防止重复初始化
const isInitialized = ref(false);

// 从缓存数据初始化补充条目
const initDataFromCache = (cacheData: any[]) => {
  if (!cacheData || cacheData.length === 0) {
    return;
  }
  dataSource.value = cacheData.map((item: any, index: number) => {
    const newKey = `cache_${Date.now()}_${index}`;

    // 处理附件数据 - 将字符串路径转换为 UploadFile 对象
    let attachmentFiles: UploadFile[] = [];
    let attachments: string[] = [];

    if (item.attachments && Array.isArray(item.attachments)) {
      attachmentFiles = item.attachments.map((path: string, fileIndex: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;

        // 如果路径已经包含完整 URL，提取相对路径
        if (path.includes(baseUrl)) {
          processedPath = path.replace(baseUrl, '');
        }

        // 确保路径以 / 开头
        if (!processedPath.startsWith('/')) {
          processedPath = '/' + processedPath;
        }

        // 保存到 attachments 数组（API 字段）
        attachments.push(processedPath);

        // 从路径提取原始文件名
        const originalFileName = path.split('/').pop() || `附件${fileIndex + 1}`;
        // 去除时间戳前缀
        const cleanedFileName = removeTimestampPrefix(originalFileName);

        return {
          uid: `${newKey}_${fileIndex}`,
          name: cleanedFileName,
          status: 'done' as const,
          url: baseUrl + processedPath,
          filePath: processedPath,
          fileName: cleanedFileName,
        };
      });
    }

    // 🔧 模仿发票组件：为现有数据生成 tempId
    const timestamp = Date.now();
    const randomSuffix = Math.floor(Math.random() * 1000);
    const uniqueId = `additionalItem_${timestamp}_${randomSuffix}`;

    const supplementItem: SupplementItem = {
      key: newKey,
      tempId: item.tempId || uniqueId, // 🔧 新增：使用现有tempId或生成新的
      serialNumber: index + 1,
      occurDate: item.occurDate ? dayjs(item.occurDate) : null, // 转换为 dayjs 对象
      itemName: item.itemName || '',
      type: item.type,
      billNum: item.billNum || null,
      billUnitPrice: item.billUnitPrice || null,
      description: item.description || '',
      attachments: attachments, // API 字段
      attachmentFiles: attachmentFiles, // UI 显示用
      invoiceTempId: item.invoiceTempId || null, // 🔧 新增：保留关联的发票ID
      statementTempId: item.statementTempId || null, // 🔧 新增：保留关联的水单ID
    };

    return supplementItem;
  });
  console.log(dataSource.value, "dataSource.value");

  isInitialized.value = true;

  // 发射数据到父组件
  emitData();
};

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';

  // 去除时间戳前缀（如：1754300525-见证性材料.png -> 见证性材料.png）
  const cleanedFileName = removeTimestampPrefix(fileName);

  const maxLength = 15;
  if (cleanedFileName.length <= maxLength) return cleanedFileName;

  const extension = cleanedFileName.split('.').pop() || '';
  const nameWithoutExt = cleanedFileName.substring(0, cleanedFileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';

  return `${truncatedName}.${extension}`;
};

// 去除文件名中的时间戳前缀
const removeTimestampPrefix = (fileName: string): string => {
  if (!fileName) return '';

  // 匹配模式：数字开头，后面跟着连字符，再跟着其他字符
  // 例如：1754300525-见证性材料.png -> 见证性材料.png
  const timestampPattern = /^(\d+)-(.+)$/;
  const match = fileName.match(timestampPattern);

  if (match) {
    return match[2]; // 返回连字符后面的部分
  }

  return fileName; // 如果没有匹配到模式，返回原文件名
};

// 文件上传处理
const uploadRequest = (options: any, itemKey: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能上传文件');
    return;
  }

  // 文件验证
  const file = options.file;
  const isValidType =
    SUPPORTED_FILE_TYPES.includes(file.type) ||
    file.name.toLowerCase().endsWith('.pdf') ||
    file.name.toLowerCase().endsWith('.doc') ||
    file.name.toLowerCase().endsWith('.docx');

  if (!isValidType) {
    message.error('只支持上传 PDF、图片、Word 文档格式的文件！');
    return;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return;
  }

  uploadLoadingMap.value[itemKey] = true;

  const formData = new FormData();
  formData.append('file', file);

  fileApi
    .upload(formData)
    .then((it) => {
      // 确保路径格式正确
      const filePath = it.path || '';
      const fullUrl = filePath ? (filePath.startsWith('/') ? baseUrl + filePath : baseUrl + '/' + filePath) : '';

      const fileObj: UploadFile = {
        uid: file.uid || Date.now().toString(),
        name: file.name,
        status: 'done',
        url: fullUrl,
        filePath: filePath, // 保存相对路径，用于删除时匹配
        fileName: file.name,
      };

      // 添加到对应条目的文件列表
      const item = dataSource.value.find((item) => item.key === itemKey);
      if (item) {
        item.attachmentFiles.push(fileObj);
        // 同时添加到API字段（保存相对路径）
        if (filePath) {
          item.attachments.push(filePath);
        }
        emitData();
      }

      message.success(`${file.name} 上传成功`);
    })
    .catch((error) => {
      message.error(`${file.name} 上传失败，请重试`);
    })
    .finally(() => {
      uploadLoadingMap.value[itemKey] = false;
    });
};

// 文件删除
const handleRemoveFile = (file: UploadFile, itemKey: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能删除文件');
    return;
  }

  const item = dataSource.value.find((item) => item.key === itemKey);
  if (item) {
    const index = item.attachmentFiles.findIndex((f) => f.uid === file.uid);
    if (index > -1) {
      // 从UI文件列表中删除
      item.attachmentFiles.splice(index, 1);

      // 从API字段中删除对应的路径
      if (file.filePath) {
        // filePath现在保存的是相对路径，直接匹配即可
        const pathIndex = item.attachments.findIndex((path) => path === file.filePath);
        if (pathIndex > -1) {
          item.attachments.splice(pathIndex, 1);
        }
      }

      emitData();
      message.success('文件删除成功');
    }
  }
};

// 文件预览
const handlePreviewFile = (file: UploadFile) => {
  previewFile.value = file;
  previewFileName.value = file.name;
  previewVisible.value = true;
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewFile.value = null;
  previewFileName.value = '';
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};

// 新增条目
const handleAddItem = () => {
  if (props.readonly) {
    message.warning('查看模式下不能添加条目');
    return;
  }

  // 🔧 模仿发票组件的做法：使用时间戳生成唯一标识
  const timestamp = Date.now();
  const randomSuffix = Math.floor(Math.random() * 1000);
  const uniqueId = `additionalItem_${timestamp}_${randomSuffix}`;
  const newKey = `new_${timestamp}`;

  const newItem: SupplementItem = {
    key: newKey,
    tempId: uniqueId, // 🔧 新增：使用时间戳生成的唯一标识
    serialNumber: dataSource.value.length + 1,
    occurDate: null,
    itemName: '', // 项目名称
    type: null,
    billNum: null,
    billUnitPrice: null,
    description: '',
    attachments: [], // API字段
    attachmentFiles: [], // UI显示用
    invoiceTempId: null, // 🔧 新增：初始化发票关联ID
    statementTempId: null, // 🔧 新增：初始化水单关联ID
  };

  dataSource.value.push(newItem);
  emitData();
};

// 删除条目
const handleDeleteItem = (key: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能删除条目');
    return;
  }

  const index = dataSource.value.findIndex((item) => item.key === key);
  if (index > -1) {
    const deletedItem = dataSource.value[index];
    dataSource.value.splice(index, 1);
    // 重新排序序号
    dataSource.value.forEach((item, idx) => {
      item.serialNumber = idx + 1;
    });
    emitData();
    message.success('条目删除成功');
  }
};

// 数据变化时发射事件
const emitData = () => {
  // 转换为API格式
  const apiData = dataSource.value.map((item, index) => ({
    tempId: item.tempId, // 🔧 修复：包含tempId字段
    occurDate: formatDate(item.occurDate),
    itemName: item.itemName,
    type: item.type,
    billNum: item.billNum || 0,
    billUnitPrice: item.billUnitPrice || 0,
    description: item.description,
    attachments: item.attachments.map((path) => {
      // 确保拼接完整的URL
      if (!path) return '';
      // 如果已经是完整URL，直接返回
      if (path.startsWith('http')) return path;
      // 如果是相对路径，拼接baseUrl
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    }),
    invoiceTempId: item.invoiceTempId || props.additionalItems[index]?.invoiceTempId, // 🔧 新增：包含发票关联ID
    statementTempId: item.statementTempId || props.additionalItems[index]?.statementTempId, // 🔧 新增：包含水单关联ID
  }));

  emit('supplementEntryEmit', apiData);
};

// 字段更新处理
const handleFieldUpdate = (key: string, field: string, value: any) => {
  const item = dataSource.value.find((item) => item.key === key);
  if (item) {
    (item as any)[field] = value;
    emitData();
  }
};

// 日期格式化
const formatDate = (date: Dayjs | string | null): string | null => {
  if (!date) return null;
  if (typeof date === 'string') return date;
  return date.format('YYYY-MM-DD');
};

// 日期变化处理
const handleDateChange = (key: string, date: Dayjs | null) => {
  const item = dataSource.value.find((item) => item.key === key);
  if (item) {
    item.occurDate = date;
    emitData();
  }
};

// 暂存
const supplementEntryTempSave = () => {
  emitData();
};

// 校验
const supplementEntrySub = () => {
  let isVerPassed = true;

  if (!dataSource.value || dataSource.value.length === 0) {
    message.error('请添加补充条目！');
    anchorJump('supplementTable');
    isVerPassed = false;
    return isVerPassed;
  }

  // 验证每个条目的必填字段
  for (const item of dataSource.value) {
    if (!item.occurDate) {
      message.error(`第${item.serialNumber}条：请选择日期！`);
      anchorJump('supplementEntryRow' + item.serialNumber);
      isVerPassed = false;
      break;
    }
    if (!item.itemName || item.itemName.trim() === '') {
      message.error(`第${item.serialNumber}条：请输入项目名称！`);
      anchorJump('supplementEntryRow' + item.serialNumber);
      isVerPassed = false;
      break;
    }
    if (item.type === null || item.type === undefined) {
      message.error(`第${item.serialNumber}条：请选择类型！`);
      anchorJump('supplementEntryRow' + item.serialNumber);
      isVerPassed = false;
      break;
    }
    if (!item.billNum || item.billNum <= 0) {
      message.error(`第${item.serialNumber}条：请输入有效数量！`);
      anchorJump('supplementEntryRow' + item.serialNumber);
      isVerPassed = false;
      break;
    }
    if (!item.billUnitPrice || item.billUnitPrice <= 0) {
      message.error(`第${item.serialNumber}条：请输入有效单价！`);
      anchorJump('supplementEntryRow' + item.serialNumber);
      isVerPassed = false;
      break;
    }
  }

  if (isVerPassed) {
    supplementEntryTempSave();
  }

  return isVerPassed;
};



defineExpose({ supplementEntrySub, supplementEntryTempSave });

onMounted(async () => {
  // 监听 schemeDetail 变化，处理缓存数据回显
  watch(
    () => props.schemeDetail,
    (newSchemeDetail) => {
      if (newSchemeDetail && newSchemeDetail.additionalItems) {
        if (props.readonly) {
          // 查看模式：直接显示数据，不走缓存逻辑
          initDataFromCache(newSchemeDetail.additionalItems);
        } else if (!isInitialized.value) {
          // 编辑模式：走缓存逻辑
          initDataFromCache(newSchemeDetail.additionalItems);
        }
      }
    },
    { immediate: true, deep: true },
  );
  // 等待一下，让 watch 先执行
  await nextTick();

  // 只有在没有缓存数据且不是查看模式时才初始化空数据
  if (dataSource.value.length === 0 && !isInitialized.value && !props.readonly) {
    handleAddItem();
  }

  // 确保初始化时发射数据
  if (!isInitialized.value) {
    emitData();
  }
});

</script>

<template>
  <!-- 补充条目 -->
  <div class="scheme_supplement_entry">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>补充条目</span>
    </div>

    <!-- 可编辑表格 -->
    <div class="info-table-wrapper supplement-table" id="supplementTable">
      <div class="table-header">
        <div class="col-serial font-color">序号</div>
        <div class="col-attachment font-color">附件</div>
        <div class="col-type font-color">类型</div>
        <div class="col-item font-color">项目</div>
        <div class="col-date font-color">日期</div>
        <div class="col-quantity font-color">数量</div>
        <div class="col-price font-color">单价</div>
        <div class="col-description font-color">描述</div>
        <div class="col-action font-color" v-if="!readonly">操作</div>
      </div>
      <div class="table-body">
        <div v-for="(item, index) in dataSource" :key="item.key" class="table-row"
          :id="'supplementEntryRow' + item.serialNumber">
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber }}
          </div>

          <!-- 附件 -->
          <div class="col-attachment">
            <div class="attachment-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item.attachmentFiles.length > 0">
                <a-tag v-for="file in item.attachmentFiles" :key="file.uid" :closable="!readonly" class="file-tag"
                  @click="() => handlePreviewFile(file)" @close="() => handleRemoveFile(file, item.key)">
                  {{ getFileDisplayName(file.name) }}
                </a-tag>
              </div>

              <!-- 上传按钮 - 查看模式下隐藏 -->
              <a-upload v-if="!readonly" :file-list="[]"
                :custom-request="(options: any) => uploadRequest(options, item.key)" :multiple="true"
                :show-upload-list="false" :accept="UPLOAD_ACCEPT">
                <a-button size="small" type="link" :loading="uploadLoadingMap[item.key] || false">
                  <upload-outlined />
                  上传
                </a-button>
              </a-upload>
            </div>
          </div>

          <!-- 类型 -->
          <div class="col-type">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{typeOptions.find((opt) => opt.value === item.type)?.label || '-'}}
            </span>
            <!-- 编辑模式：显示输入框 -->
            <a-select v-else v-model:value="item.type" placeholder="选择类型" size="small" class="borderless-input"
              :bordered="false" :options="typeOptions"
              @change="(value: any) => handleFieldUpdate(item.key, 'type', value)" />
          </div>

          <!-- 项目 -->
          <div class="col-item">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{ item.itemName || '-' }}
            </span>
            <!-- 编辑模式：显示输入框 -->
            <a-input v-else v-model:value="item.itemName" placeholder="请输入项目名称" size="small" class="borderless-input"
              :bordered="false" @change="() => handleFieldUpdate(item.key, 'itemName', item.itemName)" />
          </div>

          <!-- 日期 -->
          <div class="col-date">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{ formatDate(item.occurDate) || '-' }}
            </span>
            <!-- 编辑模式：显示日期选择器 -->
            <a-date-picker v-else v-model:value="item.occurDate" format="YYYY-MM-DD" placeholder="选择日期" size="small"
              class="borderless-input" :bordered="false"
              @change="(date: Dayjs | null) => handleDateChange(item.key, date)" />
          </div>

          <!-- 数量 -->
          <div class="col-quantity">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{ item.billNum || '-' }}
            </span>
            <!-- 编辑模式：显示数字输入框 -->
            <a-input-number v-else v-model:value="item.billNum" placeholder="数量" size="small" :min="0" :max="999999"
              :precision="0" class="borderless-input" :bordered="false"
              @change="(value: any) => handleFieldUpdate(item.key, 'billNum', value)" />
          </div>

          <!-- 单价 -->
          <div class="col-price">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{ item.billUnitPrice || '-' }}
            </span>
            <!-- 编辑模式：显示数字输入框 -->
            <a-input-number v-else v-model:value="item.billUnitPrice" placeholder="单价" size="small" :min="0"
              :max="999999.99" :precision="2" class="borderless-input" :bordered="false"
              @change="(value: any) => handleFieldUpdate(item.key, 'billUnitPrice', value)" />
          </div>

          <!-- 描述 -->
          <div class="col-description">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{ item.description || '-' }}
            </span>
            <!-- 编辑模式：显示输入框 -->
            <a-input v-else v-model:value="item.description" placeholder="请输入描述" size="small" class="borderless-input"
              :bordered="false" @change="() => handleFieldUpdate(item.key, 'description', item.description)" />
          </div>

          <!-- 操作 - 查看模式下隐藏 -->
          <div class="col-action" v-if="!readonly">
            <a-popconfirm title="确认删除该补充条目吗？" description="删除后将清除该条目的所有信息及附件" ok-text="删除" cancel-text="取消"
              @confirm="() => handleDeleteItem(item.key)">
              <a-button type="link" danger size="small">
                <img :src="hoverColor == index ? arrow : deleteGray" alt="" class="imgBig"
                  @mouseenter="hoverColor = index" @mouseleave="hoverColor = -1" />
              </a-button>
            </a-popconfirm>
          </div>
        </div>

        <!-- 添加按钮行 - 查看模式下隐藏 -->
        <div class="table-row add-row" v-if="!readonly">
          <div class="add-button-full-width" @click="handleAddItem">
            <div class="demand_add">
              <img :src="add" alt="" class="imgAddBig" style="margin-right: 5px" />
              <span>添加补充条目</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewFileName }}</h4>
        </div>
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <!-- 图片预览 -->
            <img v-if="
              previewFile.name &&
              (previewFile.name.toLowerCase().includes('.jpg') ||
                previewFile.name.toLowerCase().includes('.jpeg') ||
                previewFile.name.toLowerCase().includes('.png') ||
                previewFile.name.toLowerCase().includes('.gif'))
            " :src="previewFile.url" alt="预览图片" style="max-width: 100%; max-height: 500px; object-fit: contain" />
            <!-- PDF预览 -->
            <iframe v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url" style="width: 100%; height: 500px; border: none"></iframe>
            <!-- 其他文件类型显示下载链接 -->
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile"> 下载文件 </a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFileName }}</p>
              <p>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.imgBig {
  width: 20px;
  height: 20px;
}

.imgAddBig {
  width: 16px;
  height: 16px;
}

.font-color {
  color: #86909c;
}

.scheme_supplement_entry {
  .interact_title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;

    .interact_shu {
      width: 4px;
      height: 20px;
      background: #1868db;
      border-radius: 2px;
    }

    span {
      font-size: 18px;
      font-weight: 500;
      color: #1d2129;
    }
  }

  .info-table-wrapper {
    width: 100%;
    // border: 1px solid #d9d9d9;
    border-radius: 6px;
    margin-bottom: 16px;
    padding: 0;
    overflow: hidden;

    &.supplement-table {
      width: 100%;
    }

    .table-header {
      display: flex;
      background-color: #f2f3f5;
      font-weight: 500;
      font-size: 14px;
      color: #333;
      // border-bottom: 1px solid #d9d9d9;

      >div {
        padding: 12px 8px;
        text-align: center;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .col-serial {
        width: 80px;
      }

      .col-attachment {
        width: 250px;
      }

      .col-type {
        width: 120px;
      }

      .col-item {
        width: 200px;
      }

      .col-date {
        width: 140px;
      }

      .col-quantity {
        width: 80px;
      }

      .col-price {
        width: 80px;
      }

      .col-description {
        width: 200px;
      }

      .col-action {
        width: 60px;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #e5e6eb;
        min-height: 38px;

        &:last-child {
          border-bottom: none;
        }

        >div {
          padding: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 38px;
          // height: 38px;
        }

        &.add-row {
          border-bottom: none;
          margin-top: 5px; // 🔧 新增：距离上边5px

          .add-button-full-width {
            width: auto; // 🔧 修改：从100%改为auto，让按钮宽度自适应内容
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-height: 38px;
            cursor: pointer;
            border: none; // 🔧 修改：去掉边框
            border-radius: 6px; // 🔧 新增：添加圆角
            background-color: transparent; // 🔧 新增：透明背景
            transition: all 0.3s ease; // 🔧 新增：添加过渡效果

            &:hover {
              background-color: #f5f5f5; // 🔧 保留：悬停背景色
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              color: #1890ff;
              font-size: 14px;
              margin-left: 8px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
                background-size: contain;
                margin-right: 8px;
              }
            }
          }
        }

        >div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 40px;
          // border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }
        }

        .col-serial {
          width: 80px;
        }

        .col-attachment {
          width: 250px;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 4px;

          .attachment-content {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
            justify-content: flex-start;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: flex-start;
              max-width: 100%;

              .file-tag {
                cursor: pointer;
                font-size: 12px;
                background-color: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;

                &:hover {
                  opacity: 0.8;
                  background-color: #bae7ff;
                }
              }
            }
          }
        }

        .col-type {
          width: 120px;
        }

        .col-item {
          width: 200px;
        }

        .col-date {
          width: 140px;
        }

        .col-quantity {
          width: 80px;
        }

        .col-price {
          width: 80px;
        }

        .col-description {
          width: 200px;
        }

        .col-action {
          width: 60px;
          display: flex;
          justify-content: center;
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }

    .ant-picker-input>input {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .file-download {
        padding: 40px 0;

        p {
          margin-bottom: 16px;
          color: #666;
        }
      }

      .no-file {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }
}

// 查看模式纯文本样式
.readonly-text {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  padding: 4px 0;
  display: inline-block;
  width: 100%;
  text-align: center;
}

.mr20 {
  margin-right: 20px;
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}
</style>

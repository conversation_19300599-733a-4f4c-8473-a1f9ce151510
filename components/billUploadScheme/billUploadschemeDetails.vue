<script setup lang="ts">
// 方案互动
import { message, Modal, Affix } from 'ant-design-vue';
import { onMounted, onBeforeUnmount, ref, reactive, computed, nextTick, inject, Ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { debounce } from 'lodash';
import {
  errorModal,
  resolveParam,
  routerParam,
  numComputedArrMethod,
  meetingProcessOrchestration,
} from '@haierbusiness-front/utils';
import {
  schemeApi,
  miceBidManOrderListApi,
  miceBidBillApi,
  miceMerchantBillApi,
  miceBidManBillApi,
} from '@haierbusiness-front/apis';
import {
  miceSchemeSubmitRequest,
  ProcessOrchestrationServiceTypeEnum,
  MerchantType,
  IBillDetail,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import billUploadschemeInfo from './schemeComponent/billUploadschemeInfo.vue';
import schemeHotel from './schemeComponent/billUploadschemeHotel.vue';
import schemePlan from './schemeComponent/billUploadschemePlan.vue';
import schemeMaterial from './schemeComponent/billUploadschemeMaterial.vue';
import schemePresents from './schemeComponent/billUploadschemePresents.vue';
import schemeOther from './schemeComponent/billUploadschemeOther.vue';
import schemeServiceFee from './schemeComponent/billUploadschemeServiceFee.vue';
import schemeFiles from './schemeComponent/billUploadschemeFiles.vue';
import schemeTotal from './schemeComponent/billUploadschemeTotal.vue';
import billUploadschemeSupplementEntry from './schemeComponent/billUploadschemeSupplementEntry.vue';
import billUploadschemeHotelContract from './schemeComponent/billUploadschemeHotelContract.vue';
import billUploadschemeInvoice from './schemeComponent/billUploadschemeInvoice.vue';
import billUploadschemeWaterBill from './schemeComponent/billUploadschemeWaterBill.vue';
import billUploadschemeAccommodationDetail from './schemeComponent/billUploadschemeAccommodationDetail.vue';
import billUploadschemeConferencePhotos from './schemeComponent/billUploadschemeConferencePhotos.vue';
import billUploadschemeOtherAttachments from './schemeComponent/billUploadschemeOtherAttachments.vue';
import billUploadschemeInsuranceAttachment from './schemeComponent/billUploadschemeInsuranceAttachment.vue';
import ExportExpenseConfirmation from './schemeComponent/ExportExpenseConfirmation.vue';
import RelatedBillDialog from './schemeComponent/RelatedBillDialog.vue';

const { loginUser } = storeToRefs(applicationStore());

// 添加 props 定义
const props = defineProps({
  // 平台类型
  platformType: {
    type: String,
    default: 'merchant', // 默认服务商端
    validator: (value: string) => ['merchant', 'platform', 'user'].includes(value),
  },
  // 是否为只读模式
  readonly: {
    type: Boolean,
    default: undefined, // 默认 undefined，让内部逻辑决定
  },
});

const route = useRoute();
const router = useRouter();

// 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
type schemeKeys =
  | 'view'
  | 'notReported'
  | 'reported'
  | 'schemeView'
  | 'notBidding'
  | 'biddingView'
  | 'billUpload'
  | undefined;

const selectTab = ref<number>();
const schemeContainerRef = ref();
const schemePlanRef = ref(null);
const schemeMaterialRef = ref(null);
const schemePresentRef = ref(null);
const schemeOtherRef = ref(null);
const schemeFeeRef = ref(null);
const schemeFileRef = ref(null);
const supplementEntryRef = ref(null);
const billAttachmentRef = ref(null);
const hotelContractRef = ref(null);
const invoiceRef = ref(null);
const waterBillRef = ref(null);
const accommodationDetailRef = ref(null);
const conferencePhotosRef = ref(null);
const otherAttachmentsRef = ref(null);
const insuranceAttachmentRef = ref(null);
const ExportExpenseConfirmationRef = ref(null); // 添加结算单组件引用

const spinLoading = ref<Boolean>(false);
const schemeLoading = ref<Boolean>(false);
const subLoading = ref(false); // 完成提报

const abandonShow = ref<boolean>(false);
const abandonReason = ref<string>('');
const schemeAbandonReason = ref<string>(''); // 驳回内容反显

const hotelList = ref<Array<any>>([]); // 酒店
const schemePlanObj = ref<miceSchemeSubmitRequest>({}); // 每日计划
const schemeMaterialObj = ref<miceSchemeSubmitRequest>({}); // 布展物料
const schemePresentArr = ref<Array<any>>([]); // 礼品
const schemeOtherArr = ref<Array<any>>([]); // 其他
const schemeFeeObj = ref<miceSchemeSubmitRequest>({}); // 全单服务费
const schemeFileObj = ref<Array<any>>([]); // 附件

// 账单附件相关数据
const billHotelList = ref<Array<any>>([]); // 账单酒店列表
const attachmentContracts = ref<Array<any>>([]); // 一手合同附件数据
const invoiceList = ref<Array<any>>([]); // 发票列表
const waterBillList = ref<Array<any>>([]); // 水单列表
const accommodationDetailList = ref<Array<any>>([]); // 住宿详单列表
const conferencePhotoList = ref<Array<any>>([]); // 会议现场照片列表
const otherAttachmentList = ref<Array<any>>([]); // 其他附件列表
const additionalItems = ref<Array<any>>([]); // 补充条目列表
const insuranceAttachmentList = ref<Array<any>>([]); // 保单附件列表

// 关联账单弹框相关
const relatedBillVisible = ref(false);
const currentBillType = ref<'invoice' | 'waterBill'>('invoice');
const currentBillData = ref<any>(null);

const planPrice = ref<number>(0); // 每日计划 - 金额
const planEachPriceList = ref<Array<any>>([]); // 每日计划 - 金额
const materialPrice = ref<number>(0); // 布展物料 - 金额
const presentPrice = ref<number>(0); // 礼品 - 金额
const otherPrice = ref<number>(0); // 其他 - 金额
const totalPrice = ref<number>(0); // 全单服务费方案 - 总金额

const miceId = ref<number>();
const miceSchemeId = ref<number | null>(null);
const schemeType = ref<schemeKeys>();
const hotelLockId = ref<string>('');
const miceSchemeDemandHotelLockId = ref<string>('');

// 计算是否为只读模式
const isReadonly = computed(() => {
  // 如果外部明确传递了 readonly 参数，优先使用外部参数
  if (props.readonly !== undefined) {
    return props.readonly;
  }

  // 否则使用原有逻辑
  if (['/bidman/bill/confirm'].includes(route.path)) return true;
  // 当 schemeType 为 'view' 时，所有组件都应该是只读的
  else return schemeType.value === 'view';
});

const merchantId = ref<number>(); // 服务商Id
const merchantType = ref<number>(); // 服务商类型

const demandDetail = ref<any>({}); // 需求详情
const schemeDetail = ref<any>({}); // 方案详情

const schemeTotalInfo = ref<any>({}); // 合计详情

const processNode = ref<string>(''); // 流程节点
const showBindingScheme = ref<boolean>(true); // 展示标的方案
const showFee = ref<boolean>(false); // 全单服务费配置
const fullServiceRangeRateLimit = ref<number>(0); // 全单服务费
const fullServiceRemark = ref<string>(''); // 全单服务费
const serviceFeeSets = ref<Array<any>>([]); // 全单服务费配置项
const isCateringStandardControl = ref<string>(''); // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低

const isShowDel = ref<boolean>(true); // 展示删除按钮
const pdMainId = ref<number>(null);
const pdVerId = ref<number>(null);
const loadData = ref(true);
const changeTab = (tab: number) => {
  selectTab.value = tab;
  loadData.value = false;
  merchantType.value = tab;
  nextTick(() => {
    initializeData();
    loadData.value = true;
  });
};
// 初始化数据加载
const initializeData = async () => {
  // 根据方案类型加载对应数据
  if (
    (schemeType.value === 'reported' ||
      schemeType.value === 'notBidding' ||
      schemeType.value === 'schemeView' ||
      schemeType.value === 'biddingView' ||
      schemeType.value === 'billUpload' ||
      schemeType.value === 'view') &&
    !['/bidman/bill/confirm'].includes(route.path)
  ) {
    // 方案已提报\待竞价
    await getSchemeDetails();
  }

  await getDemandDetails();
};

// 根据平台类型调用不同的详情接口
const getDetailsByPlatformType = async (params: any) => {
  const platformType = props.platformType || 'merchant';
  console.log('platformType', platformType);

  try {
    let res: IBillDetail | undefined;

    if (platformType === 'merchant') {
      // 商户端 - 调用 billDetails 接口
      // 一个商户只会有一份账单
      const data = await miceMerchantBillApi.billDetails(params);
      res = data.length > 0 ? data[0] : ({} as IBillDetail);
    } else if (platformType === 'user') {
      let resTemp = await miceBidBillApi.billDetails({
        ...params,
      });
      tempDataList.value = resTemp.sort((a, b) => {
        return a.merchantType - b.merchantType;
      });
      if (selectTab.value == null && tempDataList.value.length > 0)
        selectTab.value = tempDataList.value?.[0]?.merchantType || [];
      if (resTemp.length > 0) {
        res = resTemp.find((item: IBillDetail) => {
          if ([1, 2].includes(merchantType.value!) && [1, 2].includes(item.merchantType)) return true;
          else if (item.merchantType == merchantType.value) return true;
        });
      } else {
        res = {} as IBillDetail;
      }
    } else if (platformType === 'platform') {
      // // 平台端 - 调用平台端接口
      let resTemp = await miceBidManBillApi.billDetails(params);
      tempDataList.value = resTemp.sort((a, b) => {
        return a.merchantType - b.merchantType;
      });
      if (selectTab.value == null && tempDataList.value.length > 0)
        selectTab.value = tempDataList.value?.[0]?.merchantType || [];
      if (resTemp.length > 0) {
        const filteredRes = resTemp.filter((item: IBillDetail) => {
          // 只保留 merchantType 为 1 或 2 的数据
          return [1, 2].includes(item.merchantType);
        });

        // 🔥 修复：返回第一个符合条件的数据对象，而不是数组
        res = filteredRes.length > 0 ? filteredRes[0] : ({} as IBillDetail);
      } else {
        res = {} as IBillDetail;
      }
    }

    return res;
  } catch (error) {
    throw error;
  }
};
const tempDataList = ref<Array<any>>([]);
const getDemandDetails = async () => {
  spinLoading.value = true;

  if (!miceId.value) {
    message.error('查询失败！');
    return;
  }

  const res: IBillDetail | undefined = await getDetailsByPlatformType({
    miceId: miceId.value,
    // miceId: 311,
  });

  invoiceList.value = res?.attachmentInvoices || [];
  waterBillList.value = res?.attachmentStatements || [];
  accommodationDetailList.value = res?.attachmentStayChecks || [];
  conferencePhotoList.value = res?.attachmentPhotos || [];
  otherAttachmentList.value = res?.attachmentOthers || [];
  insuranceAttachmentList.value = res?.insurances || [];

  // 驳回内容反显
  // schemeAbandonReason.value = schemeDetail.value?.abandonReason || null;
  await processSchemeDataEcho(res);

  schemeDetail.value = res;

  demandDetail.value = res || {};

  spinLoading.value = false;
};

const getSchemeDetails = async () => {
  // 方案详情
  schemeLoading.value = true;

  if (!miceId.value) {
    message.error('查询失败！');
    return;
  }

  // 🔥 修复：根据平台类型调用不同的接口获取方案详情
  let res;
  const platformType = props.platformType || 'merchant';

  if (platformType === 'merchant') {
    // 商户端 - 调用 billDetails 接口获取完整数据（包含 attachmentStayChecks）
    res = await miceMerchantBillApi.billDetails({
      miceId: miceId.value,
      miceSchemeId: miceSchemeId.value,
      miceSchemeDemandHotelLockId: miceSchemeDemandHotelLockId.value || null,
    });
  } else if (platformType === 'platform') {
    // 平台端 - 调用平台端接口
    res = await miceBidManBillApi.billDetails({
      miceId: miceId.value,
      miceSchemeId: miceSchemeId.value,
      miceSchemeDemandHotelLockId: miceSchemeDemandHotelLockId.value || null,
    });
  } else {
    // 用户端 - 调用原有接口
    res = await schemeApi.schemePlatDetails({
      miceId: miceId.value,
      miceSchemeId: miceSchemeId.value,
      miceSchemeDemandHotelLockId: miceSchemeDemandHotelLockId.value || null, // 锁定表id
    });
  }

  // 处理不同接口返回的数据格式
  let schemeData = null;
  if (Array.isArray(res) && res.length > 0) {
    // schemePlatDetails 返回数组格式
    schemeData = res[0];
  } else if (res && typeof res === 'object') {
    // billDetails 可能返回对象格式
    schemeData = res;
  }

  if (schemeData) {
    schemeDetail.value = schemeData;

    // 🔥 标记已添加模拟数据，防止被覆盖
    schemeDetail.value._hasMockOtherAttachments = true;

    // 驳回内容反显
    schemeAbandonReason.value = schemeDetail.value?.abandonReason || null;
    await processSchemeDataEcho(schemeDetail.value);
  }

  schemeLoading.value = false;
};

// 🔥 新增：统一的数据回显处理函数
const processSchemeDataEcho = async (schemeData: any) => {
  if (!schemeData) return;

  // 1. 酒店数据回显
  if (schemeData.hotels && schemeData.hotels.length > 0) {
    hotelList.value = schemeData.hotels.map((hotel: any) => ({
      ...hotel,
      id: hotel.id || hotel.miceDemandPushHotelId || Date.now(),
      hotelName: hotel.hotelName || '未知酒店',
    }));
  }

  // 2. 日程安排数据回显
  if (
    schemeData.stays ||
    schemeData.places ||
    schemeData.caterings ||
    schemeData.vehicles ||
    schemeData.attendants ||
    schemeData.activities ||
    schemeData.insurances
  ) {
    const planData = {
      stays: schemeData.stays || [],
      places: schemeData.places || [],
      caterings: schemeData.caterings || [],
      vehicles: schemeData.vehicles || [],
      attendants: schemeData.attendants || [],
      activities: schemeData.activities || [],
      insurances: schemeData.insurances || [],
    };

    schemePlanObj.value = { ...schemePlanObj.value, ...planData };
  }

  // 3. 布展物料数据回显
  if (schemeData.material) {
    schemeMaterialObj.value = { schemeMaterial: schemeData.material };
  }

  // 4. 礼品数据回显
  if (schemeData.presents && schemeData.presents.length > 0) {
    schemePresentArr.value = schemeData.presents;
  }

  // 5. 其他费用数据回显
  if (schemeData.others && schemeData.others.length > 0) {
    schemeOtherArr.value = schemeData.others;
  }

  // 6. 全单服务费数据回显
  if (schemeData.serviceFee) {
    schemeFeeObj.value = { serviceFee: schemeData.serviceFee };
  }

  // 7. 附件数据回显
  if (schemeData.attachmentFiles && schemeData.attachmentFiles.length > 0) {
    schemeFileObj.value = schemeData.attachmentFiles;
  }

  // 8. 账单附件数据回显（在账单上传模式和查看模式下）

  if (schemeType.value === 'billUpload' || schemeType.value === 'view') {
    await processBillAttachmentEcho(schemeData);
  }

  // 触发子组件数据回显
  await triggerChildComponentsEcho();
};

// 🔥 新增：触发子组件数据回显
const triggerChildComponentsEcho = async () => {
  // 触发酒店数据回显
  if (hotelList.value.length > 0) {
    hotelsEmit(hotelList.value);
  }

  // 触发日程安排数据回显
  if (Object.keys(schemePlanObj.value).length > 0) {
    schemePlanEmit(schemePlanObj.value);
  }

  // 触发布展物料数据回显
  if (Object.keys(schemeMaterialObj.value).length > 0) {
    schemeMaterialEmit(schemeMaterialObj.value);
  }

  // 触发礼品数据回显
  if (schemePresentArr.value.length > 0) {
    schemePresentEmit(schemePresentArr.value);
  }

  // 触发其他费用数据回显
  if (schemeOtherArr.value.length > 0) {
    schemeOtherEmit(schemeOtherArr.value);
  }

  // 触发全单服务费数据回显
  if (Object.keys(schemeFeeObj.value).length > 0) {
    schemeFeeEmit(schemeFeeObj.value);
  }

  // 触发附件数据回显
  if (schemeFileObj.value.length > 0) {
    schemeFileEmit(schemeFileObj.value);
  }

  // 触发账单附件相关数据回显
  if (schemeType.value === 'billUpload' || schemeType.value === 'view') {
    // 触发补充条目数据回显
    if (additionalItems.value.length > 0) {
      supplementEntryEmit(additionalItems.value);
    }

    // 触发一手合同数据回显
    if (attachmentContracts.value.length > 0) {
      handleHotelContractEmit(attachmentContracts.value);
    }

    // 触发发票数据回显
    if (invoiceList.value.length > 0) {
      handleInvoiceEmit(invoiceList.value);
    }

    // 触发水单数据回显
    if (waterBillList.value.length > 0) {
      handleWaterBillEmit(waterBillList.value);
    }

    // 触发住宿详单数据回显
    if (accommodationDetailList.value.length > 0) {
      handleAccommodationDetailEmit(accommodationDetailList.value);
    }

    // 触发会议照片数据回显
    if (conferencePhotoList.value.length > 0) {
      handleConferencePhotosEmit(conferencePhotoList.value);
    }

    // 触发其他附件数据回显
    if (otherAttachmentList.value.length > 0) {
      handleOtherAttachmentsEmit(otherAttachmentList.value);
    }

    // 触发保险附件数据回显
    if (insuranceAttachmentList.value.length > 0) {
      handleInsuranceAttachmentEmit(insuranceAttachmentList.value);
    }

    // 触发结算单数据回显
    if (schemeDetail.value?.balances && ExportExpenseConfirmationRef.value) {
      nextTick(() => {
        if (isReadonly.value) {
          // 查看模式使用 initFromSchemeDetail
          ExportExpenseConfirmationRef.value.initFromSchemeDetail();
        } else {
          // 编辑模式使用 initFromCache
          ExportExpenseConfirmationRef.value.initFromCache(schemeDetail.value);
        }
      });
    }
  }
};

// 🔥 新增：手动触发数据回显的方法（供外部调用）
const manualTriggerDataEcho = async (data?: any) => {
  if (data) {
    // 如果传入了数据，使用传入的数据
    await processSchemeDataEcho(data);
  } else if (schemeDetail.value && Object.keys(schemeDetail.value).length > 0) {
    // 否则使用当前的 schemeDetail 数据
    await processSchemeDataEcho(schemeDetail.value);
  } else {
    return;
  }
};

// 🔥 暴露方法供外部调用
defineExpose({
  manualTriggerDataEcho,
  processSchemeDataEcho,
  triggerChildComponentsEcho,
  tempDataList,
});

// 账单附件数据回显处理函数
const processBillAttachmentEcho = async (schemeData: any) => {
  // 处理发票数据
  if (schemeData.attachmentInvoices) {
    invoiceList.value = (schemeData.attachmentInvoices || []).map((invoice: any) => {
      const processedInvoice = {
        ...invoice,
        // 确保必要字段有默认值
        relatedBill: invoice.relatedBill || '查看>>',
        relatedAmountTotalCny: invoice.relatedAmountTotalCny || 0,
      };

      // 如果有 paths 但没有 attachmentFiles，则转换 paths 为 attachmentFiles
      if (
        invoice.paths &&
        invoice.paths.length > 0 &&
        (!invoice.attachmentFiles || invoice.attachmentFiles.length === 0)
      ) {
        processedInvoice.attachmentFiles = invoice.paths.map((path: string, index: number) => {
          return processFilePathToAttachment(path, index, invoice.tempId);
        });
      }

      return processedInvoice;
    });
  }

  // 处理水单数据
  if (schemeData.attachmentStatements) {
    waterBillList.value = (schemeData.attachmentStatements || []).map((waterBill: any) => {
      const processedWaterBill = {
        ...waterBill,
        // 确保必要字段有默认值
        relatedBill: waterBill.relatedBill || '查看>>',
        relatedAmountTotalCny: waterBill.relatedAmountTotalCny || 0,
      };

      // 如果有 paths 但没有 attachmentFiles，则转换 paths 为 attachmentFiles
      if (
        waterBill.paths &&
        waterBill.paths.length > 0 &&
        (!waterBill.attachmentFiles || waterBill.attachmentFiles.length === 0)
      ) {
        processedWaterBill.attachmentFiles = waterBill.paths.map((path: string, index: number) => {
          return processFilePathToAttachment(path, index, waterBill.tempId);
        });
      }

      return processedWaterBill;
    });
  }

  // 处理其他账单附件数据
  accommodationDetailList.value = schemeData.attachmentStayChecks || [];
  conferencePhotoList.value = schemeData.attachmentPhotos || [];
  otherAttachmentList.value = schemeData.attachmentOthers || [];
  attachmentContracts.value = schemeData.attachmentContracts || [];
  additionalItems.value = schemeData.additionalItems || [];
  insuranceAttachmentList.value = schemeData.insuranceAttachments || [];

  // 🔥 将 additionalItems 数据合并到 schemeDetail 中，确保子组件能正确接收
  if (schemeData.additionalItems && schemeData.additionalItems.length > 0) {
    schemeDetail.value = {
      ...schemeDetail.value,
      additionalItems: schemeData.additionalItems,
    };
  }

  // 🔥 修复：无论数据是否为空，都要合并到 schemeDetail 中，确保组件能接收到正确的数据结构
  schemeDetail.value = {
    ...schemeDetail.value,
    attachmentStayChecks: schemeData.attachmentStayChecks || [],
    attachmentStatements: schemeData.attachmentStatements || schemeDetail.value.attachmentStatements || [],
    attachmentPhotos: schemeData.attachmentPhotos || schemeDetail.value.attachmentPhotos || [],
    attachmentOthers: schemeData.attachmentOthers || schemeDetail.value.attachmentOthers || [],
    attachmentContracts: schemeData.attachmentContracts || [],
    balances: schemeData.balances || [], // 结算单数据
  };

  // 🔥 同时更新 accommodationDetailList 变量
  accommodationDetailList.value = schemeData.attachmentStayChecks || [];
};

// 🔥 新增：文件路径转换为附件对象的工具函数
const processFilePathToAttachment = (path: string, index: number, tempId?: string) => {
  // 处理路径，确保正确的 URL 格式
  let processedPath = path;
  const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';

  // 如果路径已经包含完整 URL，提取相对路径
  if (path.includes(baseUrl)) {
    processedPath = path.replace(baseUrl, '');
  }

  // 确保路径以 / 开头
  if (!processedPath.startsWith('/')) {
    processedPath = '/' + processedPath;
  }

  return {
    uid: `${tempId || Date.now()}_${index}`,
    name: path.split('/').pop() || `附件${index + 1}`,
    status: 'done' as const,
    url: baseUrl + processedPath,
    filePath: processedPath,
    fileName: path.split('/').pop() || `附件${index + 1}`,
  };
};

// 酒店
const hotelsEmit = (hotelArr: Array<any>) => {
  hotelList.value = [...hotelArr];
};
// 日程安排
const schemePlanEmit = (miceSchemeSubData: miceSchemeSubmitRequest) => {
  schemePlanObj.value = { ...miceSchemeSubData };
};
// 布展物料
const schemeMaterialEmit = (materialObj: miceSchemeSubmitRequest) => {
  schemeMaterialObj.value = { ...materialObj };
};
// 礼品
const schemePresentEmit = (presentArr: Array<any>) => {
  schemePresentArr.value = [...presentArr];
};
// 其他
const schemeOtherEmit = (otherArr: Array<any>) => {
  schemeOtherArr.value = [...otherArr];
};
// 全单服务费
const schemeFeeEmit = (feeObj: miceSchemeSubmitRequest) => {
  schemeFeeObj.value = { ...feeObj };
  schemeTotalInfo.value = { ...schemeTotalInfo.value, ...feeObj };
};
// 附件
const schemeFileEmit = (arr: Array<any>) => {
  schemeFileObj.value = [...arr];
};

// 补充条目
const supplementEntryEmit = (data: any) => {
  additionalItems.value = data;
};

// 账单附件相关事件处理
const handleHotelContractEmit = (data: any) => {
  // 避免重复赋值，只在数据确实不同时才更新
  if (JSON.stringify(attachmentContracts.value) !== JSON.stringify(data)) {
    attachmentContracts.value = data;
  }
};

const handleInvoiceEmit = (data: any) => {
  invoiceList.value = data;
};

const handleWaterBillEmit = (data: any) => {
  waterBillList.value = data;
};

const handleAccommodationDetailEmit = (data: any) => {
  accommodationDetailList.value = data;
};

const handleConferencePhotosEmit = (data: any) => {
  conferencePhotoList.value = data;
};

const handleOtherAttachmentsEmit = (data: any) => {
  otherAttachmentList.value = data;
};

const handleInsuranceAttachmentEmit = (data: any) => {
  insuranceAttachmentList.value = data;
};

// 处理保险附件临时ID
const handleInsuranceAttachmentTempId = (data: any) => {
  // 将拼接好的附件数据传递给所有保险组件
  // 通过 schemePlanRef -> insuranceRef 的路径访问保险组件
  if (schemePlanRef.value && schemePlanRef.value.insuranceRef) {
    const insuranceComponents = schemePlanRef.value.insuranceRef;
    if (insuranceComponents && insuranceComponents.length > 0) {
      // 遍历所有保险组件实例，为每个都添加附件
      insuranceComponents.forEach((insuranceComponent, index) => {
        if (insuranceComponent && insuranceComponent.updateInsuranceAttachmentId) {
          // 传递完整的附件数据对象，索引传0（因为每个组件内部会处理所有项目）
          insuranceComponent.updateInsuranceAttachmentId(0, data);
        }
      });
    }
  }
};

// 计算实际总签到人数
const totalCheckInPersonNum = computed(() => {
  if (!accommodationDetailList.value || accommodationDetailList.value.length === 0) {
    return 0;
  }
  return accommodationDetailList.value.reduce((total, item) => {
    return total + (item.checkInPersonNum || 0);
  }, 0);
});

// 判断是否有保险数据
const hasInsuranceData = computed(() => {
  return demandDetail.value?.insurances && demandDetail.value.insurances.length > 0;
});

// 查看住宿详单
const viewAccommodationDetail = () => {
  // 滚动到住宿详单组件
  const accommodationDetailElement = document.querySelector('.interact_accommodation_detail');
  if (accommodationDetailElement) {
    accommodationDetailElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    });
  }
};

const handleViewRelatedBill = (item: any) => {
  currentBillData.value = item;

  // 🔥 优化：更准确的账单类型判断逻辑
  let billType: 'invoice' | 'waterBill' = 'invoice'; // 默认值

  // 方法1：通过 tempId 在列表中查找
  if (invoiceList.value.find((invoice: any) => invoice.tempId === item.tempId)) {
    billType = 'invoice';
  } else if (waterBillList.value.find((waterBill: any) => waterBill.tempId === item.tempId)) {
    billType = 'waterBill';
  }
  // 方法2：在查看模式下，通过 schemeDetail 中的数据判断
  else if (isReadonly.value) {
    // 在发票数据中查找
    if (
      schemeDetail.value?.attachmentInvoices?.find(
        (invoice: any) => invoice.id === item.id || invoice.tempId === item.tempId,
      )
    ) {
      billType = 'invoice';
    }
    // 在水单数据中查找
    else if (
      schemeDetail.value?.attachmentStatements?.find(
        (statement: any) => statement.id === item.id || statement.tempId === item.tempId,
      )
    ) {
      billType = 'waterBill';
    }
  }
  // 方法3：通过 tempId 的前缀判断（如果有规律的话）
  else if (item.tempId) {
    if (item.tempId.includes('invoice') || item.tempId.includes('发票')) {
      billType = 'invoice';
    } else if (item.tempId.includes('water') || item.tempId.includes('statement') || item.tempId.includes('水单')) {
      billType = 'waterBill';
    }
  }

  currentBillType.value = billType;

  relatedBillVisible.value = true;
};

const handleRelatedBillConfirm = (data: any) => {
  relatedBillVisible.value = false;
};

// 处理所有类型项目的发票/水单关联
const handleUpdateStaysInvoiceId = (data: {
  invoiceTempId: string;
  billType: 'invoice' | 'waterBill';
  selectedItemsByType: {
    stays: string[];
    places: string[];
    caterings: string[];
    vehicles: string[];
    attendants: string[];
    activities: string[];
  };
}) => {
  // 确定要更新的字段名
  const fieldToUpdate = data.billType === 'invoice' ? 'invoiceTempId' : 'statementTempId';

  let totalUpdatedCount = 0;

  // 更新schemePlanObj中的各类型数据
  if (schemePlanObj.value) {
    // 更新住宿数据
    if (schemePlanObj.value.stays && data.selectedItemsByType.stays.length > 0) {
      schemePlanObj.value.stays.forEach((stay: any, index: number) => {
        const stayId = `stay_${stay.id || stay.tempId || stay.miceDemandStayId || stay.miceSchemeStayId}`;

        if (data.selectedItemsByType.stays.includes(stayId)) {
          const numericId = extractNumericId(data.invoiceTempId);
          stay[fieldToUpdate] = numericId;
          totalUpdatedCount++;
        }
      });
    }

    // 更新会场数据
    if (schemePlanObj.value.places && data.selectedItemsByType.places.length > 0) {
      schemePlanObj.value.places.forEach((place: any) => {
        const placeId = `place_${place.id || place.tempId || place.miceDemandPlaceId || place.miceSchemePlaceId}`;
        if (data.selectedItemsByType.places.includes(placeId)) {
          const numericId = extractNumericId(data.invoiceTempId);
          place[fieldToUpdate] = numericId;
          totalUpdatedCount++;
        }
      });
    }

    // 更新用餐数据
    if (schemePlanObj.value.caterings && data.selectedItemsByType.caterings.length > 0) {
      schemePlanObj.value.caterings.forEach((catering: any) => {
        const cateringId = `catering_${
          catering.id || catering.tempId || catering.miceDemandCateringId || catering.miceSchemeCateringId
        }`;
        if (data.selectedItemsByType.caterings.includes(cateringId)) {
          const numericId = extractNumericId(data.invoiceTempId);
          catering[fieldToUpdate] = numericId;
          totalUpdatedCount++;
        }
      });
    }

    // 更新用车数据
    if (schemePlanObj.value.vehicles && data.selectedItemsByType.vehicles.length > 0) {
      schemePlanObj.value.vehicles.forEach((vehicle: any) => {
        const vehicleId = `vehicle_${
          vehicle.id || vehicle.tempId || vehicle.miceDemandVehicleId || vehicle.miceSchemeVehicleId
        }`;
        if (data.selectedItemsByType.vehicles.includes(vehicleId)) {
          const numericId = extractNumericId(data.invoiceTempId);
          vehicle[fieldToUpdate] = numericId;
          totalUpdatedCount++;
        }
      });
    }

    // 更新服务人员数据
    if (schemePlanObj.value.attendants && data.selectedItemsByType.attendants.length > 0) {
      schemePlanObj.value.attendants.forEach((attendant: any) => {
        const attendantId = `attendant_${
          attendant.id || attendant.tempId || attendant.miceDemandAttendantId || attendant.miceSchemeAttendantId
        }`;
        if (data.selectedItemsByType.attendants.includes(attendantId)) {
          const numericId = extractNumericId(data.invoiceTempId);
          attendant[fieldToUpdate] = numericId;
          totalUpdatedCount++;
        }
      });
    }

    // 更新活动数据
    if (schemePlanObj.value.activities && data.selectedItemsByType.activities.length > 0) {
      schemePlanObj.value.activities.forEach((activity: any) => {
        const activityId = `activity_${
          activity.id || activity.tempId || activity.miceDemandActivityId || activity.miceSchemeActivityId
        }`;
        if (data.selectedItemsByType.activities.includes(activityId)) {
          const numericId = extractNumericId(data.invoiceTempId);
          activity[fieldToUpdate] = numericId;
          totalUpdatedCount++;
        }
      });
    }

    // 触发数据更新
    schemePlanEmit(schemePlanObj.value);
  }
};

// 处理关联金额更新
const handleUpdateRelatedAmount = (data: {
  invoiceTempId: string;
  billType: 'invoice' | 'waterBill';
  totalAmount: number;
  relatedBills: any[];
}) => {
  if (data.billType === 'invoice') {
    // 更新发票的关联金额合计和关联账单数据
    if (invoiceRef.value && invoiceRef.value.updateRelatedAmount) {
      invoiceRef.value.updateRelatedAmount(data.invoiceTempId, data.totalAmount, data.relatedBills);
    }
  } else if (data.billType === 'waterBill') {
    // 更新水单的关联金额合计和关联账单数据
    if (waterBillRef.value && waterBillRef.value.updateRelatedAmount) {
      waterBillRef.value.updateRelatedAmount(data.invoiceTempId, data.totalAmount, data.relatedBills);
    }
  }
};

// 提取原始账单ID的辅助函数
const extractOriginalBillId = (billId: string): string => {
  // 如果是新添加的格式 'new_timestamp_originalId'，提取原始ID
  if (billId.startsWith('new_')) {
    const parts = billId.split('_');
    if (parts.length >= 3) {
      return parts.slice(2).join('_'); // 去掉 'new_' 和时间戳部分
    }
  }
  return billId;
};

// 提取发票/水单ID中的数字部分
const extractNumericId = (tempId: string): string => {
  // 从 'invoice_1753756809308_326' 格式中提取 '1753756809308'
  if (tempId && tempId.includes('_')) {
    const parts = tempId.split('_');
    if (parts.length >= 2) {
      // 返回第二部分（时间戳数字）
      return parts[1];
    }
  }
  return tempId;
};

// 处理提交数据中的所有invoiceTempId和statementTempId字段
const processSubmitDataIds = (data: any): any => {
  if (!data) return data;

  // 如果是数组，递归处理每个元素
  if (Array.isArray(data)) {
    return data.map((item) => processSubmitDataIds(item));
  }

  // 如果是对象，处理其属性
  if (typeof data === 'object') {
    const processedData = { ...data };

    // 处理invoiceTempId字段
    if (processedData.invoiceTempId && typeof processedData.invoiceTempId === 'string') {
      processedData.invoiceTempId = extractNumericId(processedData.invoiceTempId);
    }

    // 处理statementTempId字段
    if (processedData.statementTempId && typeof processedData.statementTempId === 'string') {
      processedData.statementTempId = extractNumericId(processedData.statementTempId);
    }

    // 递归处理嵌套对象
    Object.keys(processedData).forEach((key) => {
      if (typeof processedData[key] === 'object') {
        processedData[key] = processSubmitDataIds(processedData[key]);
      }
    });

    return processedData;
  }

  return data;
};

// 收集所有已关联的账单ID（排除当前正在编辑的发票/水单）
const getAllRelatedBillIds = (excludeTempId?: string): string[] => {
  const relatedBillIds: string[] = [];

  // 收集所有发票的关联账单ID
  if (invoiceList.value) {
    invoiceList.value.forEach((invoice: any) => {
      if (invoice.tempId !== excludeTempId && invoice.relatedBills) {
        invoice.relatedBills.forEach((bill: any) => {
          const originalId = extractOriginalBillId(bill.id);
          if (originalId && !relatedBillIds.includes(originalId)) {
            relatedBillIds.push(originalId);
          }
        });
      }
    });
  }

  // 收集所有水单的关联账单ID
  if (waterBillList.value) {
    waterBillList.value.forEach((waterBill: any) => {
      if (waterBill.tempId !== excludeTempId && waterBill.relatedBills) {
        waterBill.relatedBills.forEach((bill: any) => {
          const originalId = extractOriginalBillId(bill.id);
          if (originalId && !relatedBillIds.includes(originalId)) {
            relatedBillIds.push(originalId);
          }
        });
      }
    });
  }

  return relatedBillIds;
};

// 账单附件（保持兼容性）
const billAttachmentEmit = (data: any) => {};

// 每日计划-方案金额
const planPriceEmit = (priceNum: number) => {
  planPrice.value = priceNum;

  totalPriceFn();
};
// 每日计划-各单项-方案金额
const planEachPriceEmit = (arr: Array<any>) => {
  planEachPriceList.value = arr;
};
// 布展物料-方案金额
const materialPriceEmit = (priceNum: number) => {
  materialPrice.value = priceNum;

  totalPriceFn();
};
// 礼品-方案金额
const presentPriceEmit = (priceNum: number) => {
  presentPrice.value = priceNum;

  totalPriceFn();
};
// 其他-方案金额
const otherPriceEmit = (priceNum: number) => {
  otherPrice.value = priceNum;

  totalPriceFn();
};
// 全单服务费方案 - 总金额
const totalPriceFn = debounce(() => {
  totalPrice.value = planPrice.value + materialPrice.value + presentPrice.value + otherPrice.value;

  //   '%c [ planPrice.value + materialPrice.value + presentPrice.value + otherPrice.value ]-129',
  //   'font-size:13px; background:pink; color:#bf2c9f;',
  //   '每日计划',
  //   planPrice.value,
  //   '物料方案',
  //   materialPrice.value,
  //   '礼品',
  //   presentPrice.value,
  //   '其他',
  //   otherPrice.value,
  //   '总价格 - ',
  //   totalPrice.value,
  // );

  // 计算合计
  handleTotal();
}, 300);

const handleTotal = debounce(() => {
  // 计算合计数据
  const schemeAllPrice =
    totalPrice.value +
    (schemeFeeObj.value.serviceFee && schemeFeeObj.value.serviceFee.schemeServiceFeeReal
      ? schemeFeeObj.value.serviceFee.schemeServiceFeeReal
      : 0);

  const params = {
    miceId: miceId.value,
    sourceId: demandDetail.value.sourceId,
    mainCode: demandDetail.value.mainCode,
    miceSchemeId: demandDetail.value.id,
    billTotalPrice: schemeAllPrice.toFixed(2),
    schemeTotalPrice: schemeAllPrice.toFixed(2),
    remarks: demandDetail.value.remarks,
    startDate: demandDetail.value.startDate,
    endDate: demandDetail.value.endDate,
    hotels: [...hotelList.value],
    ...schemePlanObj.value,
    material: { ...schemeMaterialObj.value?.schemeMaterial },
    traffic: {},
    presents: [...schemePresentArr.value],
    others: [...schemeOtherArr.value],
    ...schemeFeeObj.value,
    attachmentContracts: [...attachmentContracts.value],
    additionalItems: [...additionalItems.value],
    attachmentStatements: waterBillRef.value ? (waterBillRef.value as any).getWaterBillDataForSubmit() : [],
    attachmentInvoices: invoiceRef.value ? invoiceRef.value.getInvoiceDataForSubmit() : [],
    attachmentStayChecks: accommodationDetailRef.value ? (accommodationDetailRef.value as any).getSubmitData() : [],
    attachmentPhotos: conferencePhotoList.value
      .map((item) => ({
        subType: item.subType,
        paths: item.paths,
      }))
      .filter((item) => item.subType && item.paths.length > 0),
    attachmentOthers: otherAttachmentList.value
      .map((item) => ({
        subType: item.description,
        paths: item.paths,
      }))
      .filter((item) => item.subType && item.paths.length > 0),

    // 结算单信息
    balances: schemeDetail.value?.balances || [],
  };

  // 合计
  schemeTotalInfo.value = params || {};
}, 300);

const getUser = async () => {
  // 获取登录服务商的类型
  const res = await schemeApi.getMerchantByUser({});

  // 服务商的类型
  // 1-酒店,2-旅行社,3-保险,4-礼品,5-用车
  merchantType.value = res.merchantType;
  merchantId.value = res.id;
};

// 流程详情
const getProcessDetails = async (
  processId = localStorage.getItem('processId') || '',
  verId = '',
  pdmMerchantPoolId = '',
) => {
  // 流程ID
  if (!processId) {
    message.error('流程ID不存在！');
    return;
  }

  let res;
  try {
    res = await miceBidManOrderListApi.processDetails({
      id: processId,
      verId: verId,
    });
  } catch (error) {
    return;
  }

  // 需求配置
  const demandProcessList = ProcessOrchestrationServiceTypeEnum.getTypeOptions().map((e) => {
    return e.value;
  });
  const demandSets = numComputedArrMethod(res.items, [...demandProcessList]);
  // 需求配置 - 全单服务费是否配置
  showFee.value = demandSets.includes(2048);

  // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
  isCateringStandardControl.value = meetingProcessOrchestration(
    'SCHEME_SUBMIT',
    res.nodes || [],
    'schemeSubmitMealLabelConfigDefine',
  );

  if (!showFee.value) {
    // 未设置全单服务费
    fullServiceRangeRateLimit.value = 0;
  } else {
    // 需求配置 - 全单服务费上限
    const feeConfigList = res.merchantPools || [];
    const feeRange = feeConfigList.filter((e) => e.id === pdmMerchantPoolId) || [];
    // 全单服务费配置项
    serviceFeeSets.value = numComputedArrMethod(feeRange[0]?.fullServiceRange - 2048, [...demandProcessList]);
    // serviceFeeSets.value = [1, 2, 4, 16];
    fullServiceRangeRateLimit.value = feeRange[0]?.fullServiceRangeRateLimit;
    fullServiceRemark.value = feeRange[0]?.fullServiceRemark;
  }
};

onMounted(async () => {
  const record = resolveParam(route.query.record as string);

  miceId.value = record.miceId;
  miceSchemeId.value = record.miceSchemeId || null;
  schemeType.value = record.schemeType || 'view'; // 🔥 如果没有 schemeType，默认设置为 'view'
  hotelLockId.value = record.hotelLockId;
  miceSchemeDemandHotelLockId.value = record.miceSchemeDemandHotelLockId;
  pdMainId.value = record.pdMainId;
  pdVerId.value = record.pdVerId;
  if (props.platformType === 'user') {
    merchantType.value = 1;
  } else if (props.platformType === 'platform') {
    // 平台端：设置为1，以便显示所有相关组件（旅行社和酒店的数据都会显示）
    merchantType.value = 1;
  } else {
    await getUser();
  }

  isShowDel.value = localStorage.getItem('testProcessSignForCiCi') === '1';
  // 初始化数据加载
  await initializeData();

  // 初始化账单附件数据
  if (schemeType.value === 'billUpload') {
    // 初始化发票和水单列表
    invoiceList.value = [];
    waterBillList.value = [];

    // 初始化新增组件数据
    accommodationDetailList.value = [];
    conferencePhotoList.value = [];
    otherAttachmentList.value = [];

    // 酒店列表将在 getDemandDetails 中从接口数据获取
    billHotelList.value = [];
    // 初始化一手合同附件数据
    attachmentContracts.value = [];
    additionalItems.value = [];
  }
});
</script>

<template>
  <!-- 方案互动 -->
  <div class="scheme_interact" ref="schemeContainerRef">
    <a-spin :spinning="spinLoading || schemeLoading || subLoading" tip="Loading..." size="large">
      <a-alert
        v-if="schemeAbandonReason"
        class="mb16 demand_reject_reason"
        message="驳回原因："
        :description="schemeAbandonReason"
        show-icon
        type="warning"
      />
      <!-- 顶部 -->
      <billUploadschemeInfo class="interact_header" :demandInfo="demandDetail" :readonly="isReadonly" />

      <div class="custom-tabs" v-if="['/bidman/bill/confirm'].includes(route.path)">
        <div class="tab-container">
          <div
            v-for="item in tempDataList"
            :key="item?.merchantType"
            v-show="item?.merchantType"
            class="tab-item"
            :class="{ active: selectTab === item?.merchantType }"
            @click="changeTab(item?.merchantType)"
          >
            <span>{{ MerchantType.ofType(item.merchantType)?.desc }}</span>
            <div class="tab-pointer" v-if="selectTab === item?.merchantType"></div>
          </div>
        </div>
      </div>

      <div class="interact_content mt20" v-if="loadData">
        <!-- 标题 -->
        <a-affix :offset-top="0" :target="() => schemeContainerRef">
          <div class="interact_demand_title mb12 pb12">
            <div class="plan_title" v-show="showBindingScheme">
              <img src="@/assets/image/common/demand_icon.png" width="16" />
              <span class="ml12">
                {{ schemeType !== 'notBidding' && schemeType !== 'biddingView' ? '最终执行方案' : '标的方案' }}
              </span>
            </div>
            <div class="plan_title">
              <img src="@/assets/image/common/plan_icon.png" width="16" />
              <span class="ml12">
                {{ schemeType !== 'notBidding' && schemeType !== 'biddingView' ? '我的账单' : '我的竞价' }}
              </span>
            </div>
          </div>
        </a-affix>

        <!-- 实际总签到人数 -->
        <div class="total_checkin_info mb16">
          <div class="checkin_content">
            <span class="checkin_label">实际总签到人数：</span>
            <span class="checkin_number">{{ totalCheckInPersonNum }}人</span>
            <a-button type="link" size="small" class="view_btn" @click="viewAccommodationDetail"> 查看 </a-button>
          </div>
        </div>

        <!-- 酒店需求 -->
        <div class="interact_hotel common_content p24 mb16" v-if="merchantType === 1 || merchantType === 2">
          <schemeHotel
            :showBindingScheme="showBindingScheme"
            :hotels="demandDetail.hotels"
            :schemeHotels="schemeDetail.hotels"
            :schemeType="schemeType"
            :merchantType="merchantType"
            :readonly="isReadonly"
            @hotelsEmit="hotelsEmit"
          />
        </div>
        <!-- 日程安排 -->
        <div class="interact_schedule_plan common_content p24 mb16">
          <schemePlan
            ref="schemePlanRef"
            v-if="merchantType !== 4"
            :showBindingScheme="showBindingScheme"
            :schemeContainerRef="schemeContainerRef"
            :processNode="processNode"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :schemeCacheInfo="schemeDetail"
            :hotelList="hotelList"
            :merchantType="merchantType"
            :isCateringStandardControl="isCateringStandardControl"
            :readonly="isReadonly"
            @planPriceEmit="planPriceEmit"
            @planEachPriceEmit="planEachPriceEmit"
            @schemePlanEmit="schemePlanEmit"
          />
        </div>
        <!-- 保单附件 -->
        <div
          v-if="hasInsuranceData && merchantType === 3 && !['/bidman/bill/confirm'].includes(route.path)"
          class="interact_insurance_attachment common_content p24 mb16"
        >
          <bill-uploadscheme-insurance-attachment
            ref="insuranceAttachmentRef"
            :schemeType="schemeType"
            :schemeItem="demandDetail"
            :schemeIndex="0"
            :attachmentList="insuranceAttachmentList"
            :schemeCacheItem="schemeDetail"
            :readonly="isReadonly"
            @attachmentEmit="handleInsuranceAttachmentEmit"
            @attachmentTempIdEmit="handleInsuranceAttachmentTempId"
          />
        </div>
        <!-- 布展物料 -->
        <div
          v-if="
            demandDetail.material &&
            demandDetail.material.materialDetails &&
            demandDetail.material.materialDetails.length > 0 &&
            (merchantType === 1 || merchantType === 2)
          "
          class="interact_wu common_content p24 mb16"
        >
          <scheme-material
            ref="schemeMaterialRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :schemeCacheInfo="schemeDetail"
            :readonly="isReadonly"
            :platformType="platformType"
            @materialPriceEmit="materialPriceEmit"
            @schemeMaterialEmit="schemeMaterialEmit"
          />
        </div>
        <!-- 礼品 -->
        <div
          v-if="demandDetail.presents && demandDetail.presents.length > 0 && merchantType === 4"
          class="interact_gift common_content p24 mb16"
        >
          <!-- 礼品组件 - 按模式分离数据传递 -->
          <!-- 查看模式：只传递 schemeDetail，避免数据冲突 -->
          <scheme-presents
            v-if="isReadonly"
            ref="schemePresentRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :schemeDetail="schemeDetail"
            :readonly="true"
            :platformType="platformType"
            @presentPriceEmit="presentPriceEmit"
            @schemePresentEmit="schemePresentEmit"
          />
          <!-- 编辑模式：传递 schemeCacheInfo，确保编辑功能正常 -->
          <scheme-presents
            v-else
            ref="schemePresentRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :schemeCacheInfo="schemeDetail"
            :readonly="false"
            :platformType="platformType"
            @presentPriceEmit="presentPriceEmit"
            @schemePresentEmit="schemePresentEmit"
          />
        </div>
        <!-- 其他 -->
        <div
          v-if="demandDetail.others && demandDetail.others.length > 0 && (merchantType === 1 || merchantType === 2)"
          class="interact_other common_content p24 mb16"
        >
          <scheme-other
            ref="schemeOtherRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :schemeCacheInfo="schemeDetail"
            :readonly="isReadonly"
            :platformType="platformType"
            @otherPriceEmit="otherPriceEmit"
            @schemeOtherEmit="schemeOtherEmit"
          />
        </div>
        <!-- 全单服务费方案 -->
        <div
          class="interact_service_fee common_content p24 mb16"
          v-if="
            ((merchantType === 1 || merchantType === 2) && showFee) || ['/bidman/bill/confirm'].includes(route.path)
          "
        >
          <scheme-service-fee
            ref="schemeFeeRef"
            :schemeType="schemeType"
            :demandInfo="demandDetail"
            :materialPrice="materialPrice"
            :presentPrice="presentPrice"
            :otherPrice="otherPrice"
            :planEachPriceList="planEachPriceList"
            :fullServiceRangeRateLimit="fullServiceRangeRateLimit"
            :fullServiceRemark="fullServiceRemark"
            :serviceFeeSets="serviceFeeSets"
            :cacheServiceFee="schemeDetail.serviceFee"
            :schemeCacheInfo="schemeDetail"
            :readonly="isReadonly"
            :platformType="platformType"
            @schemeFeeEmit="schemeFeeEmit"
          />
        </div>
        <!-- 价格见证性材料 -->
        <div
          class="interact_service_file common_content p24 mb16"
          v-if="processNode === 'BIDDING' && schemeType === 'notBidding'"
        >
          <schemeFiles ref="schemeFileRef" @schemeFileEmit="schemeFileEmit" />
        </div>
        <!-- 合计 -->
        <div class="interact_total_table common_content p24">
          <scheme-total :merchantType="merchantType" :schemeCacheInfo="schemeTotalInfo" :totalPrice="totalPrice" />
        </div>
        <!-- 账单上传相关功能 -->
        <div
          v-if="schemeType === 'billUpload' || schemeType === 'view'"
          class="bill-upload-sections"
          style="margin-top: 20px"
        >
          <!-- 补充条目 -->
          <div
            class="interact_supplement_entry common_content p24 mb16"
            v-if="merchantType === 1 || merchantType === 2"
          >
            <billUploadschemeSupplementEntry
              ref="supplementEntryRef"
              :miceId="miceId"
              :schemeDetail="schemeDetail"
              :readonly="isReadonly"
              @supplementEntryEmit="supplementEntryEmit"
            />
          </div>

          <!-- 费用确认导出（结算单） -->
          <div class="interact_export_confirmation common_content mb16">
            <ExportExpenseConfirmation
              ref="ExportExpenseConfirmationRef"
              :schemeType="schemeType"
              :schemeDetail="schemeDetail"
              :readonly="isReadonly"
            />
          </div>
          <!-- 一手合同 -->
          <div class="interact_hotel_contract common_content mb16" v-if="merchantType === 1 || merchantType === 2">
            <billUploadschemeHotelContract
              ref="hotelContractRef"
              :hotelList="billHotelList"
              :contractData="attachmentContracts"
              :readonly="isReadonly"
              @hotelContractEmit="handleHotelContractEmit"
            />
          </div>

          <!-- 发票信息 -->
          <div class="interact_invoice common_content mb16" v-if="merchantType === 1 || merchantType === 2">
            <!-- 发票组件 - 按模式分离数据传递 -->
            <!-- 查看模式：只传递 schemeDetail，避免数据冲突 -->
            <billUploadschemeInvoice
              v-if="isReadonly"
              ref="invoiceRef"
              :schemeDetail="schemeDetail"
              :invoiceList="invoiceList"
              :readonly="true"
              @invoiceEmit="handleInvoiceEmit"
              @viewRelatedBill="handleViewRelatedBill"
            />
            <!-- 编辑模式：只传递 invoiceList，确保编辑功能正常 -->
            <billUploadschemeInvoice
              v-else
              ref="invoiceRef"
              :invoiceList="invoiceList"
              :readonly="false"
              @invoiceEmit="handleInvoiceEmit"
              @viewRelatedBill="handleViewRelatedBill"
            />
          </div>

          <!-- 水单信息 -->
          <div class="interact_water_bill common_content mb16" v-if="merchantType === 1 || merchantType === 2">
            <!-- 水单组件 - 按模式分离数据传递 -->
            <!-- 查看模式：只传递 schemeDetail，避免数据冲突 -->
            <billUploadschemeWaterBill
              v-if="isReadonly"
              ref="waterBillRef"
              :schemeDetail="schemeDetail"
              :waterBillList="waterBillList"
              :readonly="true"
              @waterBillEmit="handleWaterBillEmit"
              @viewRelatedBill="handleViewRelatedBill"
            />
            <!-- 编辑模式：只传递 waterBillList，确保编辑功能正常 -->
            <billUploadschemeWaterBill
              v-else
              ref="waterBillRef"
              :waterBillList="waterBillList"
              :readonly="false"
              @waterBillEmit="handleWaterBillEmit"
              @viewRelatedBill="handleViewRelatedBill"
            />
          </div>

          <!-- 住宿详单 -->
          <div
            class="interact_accommodation_detail common_content p24 mb16"
            v-if="merchantType === 1 || merchantType === 2"
          >
            <!-- 查看模式：只传递 schemeDetail，避免数据冲突 -->
            <billUploadschemeAccommodationDetail
              v-if="isReadonly"
              ref="accommodationDetailRef"
              :schemeDetail="schemeDetail"
              :readonly="true"
              @accommodationDetailEmit="handleAccommodationDetailEmit"
            />
            <!-- 编辑模式：只传递 accommodationDetailList，确保编辑功能正常 -->
            <billUploadschemeAccommodationDetail
              v-else
              ref="accommodationDetailRef"
              :accommodationDetailList="accommodationDetailList"
              :readonly="false"
              @accommodationDetailEmit="handleAccommodationDetailEmit"
            />
          </div>

          <!-- 会议现场照片 -->
          <div
            class="interact_conference_photos common_content p24 mb16"
            v-if="merchantType === 1 || merchantType === 2"
          >
            <!-- 会议现场照片组件 - 按模式分离数据传递 -->
            <!-- 查看模式：只传递 schemeDetail，避免数据冲突 -->
            <billUploadschemeConferencePhotos
              v-if="isReadonly"
              ref="conferencePhotosRef"
              :schemeDetail="schemeDetail"
              :readonly="true"
              @conferencePhotosEmit="handleConferencePhotosEmit"
            />
            <!-- 编辑模式：只传递 conferencePhotoList，确保编辑功能正常 -->
            <billUploadschemeConferencePhotos
              v-else
              ref="conferencePhotosRef"
              :conferencePhotoList="conferencePhotoList"
              :readonly="false"
              @conferencePhotosEmit="handleConferencePhotosEmit"
            />
          </div>

          <!-- 其他附件 -->
          <div
            class="interact_other_attachments common_content p24 mb16"
            v-if="merchantType === 1 || merchantType === 2"
          >
            <!-- 其他附件组件 - 按模式分离数据传递 -->
            <!-- 查看模式：只传递 schemeDetail，避免数据冲突 -->
            <billUploadschemeOtherAttachments
              v-if="isReadonly"
              ref="otherAttachmentsRef"
              :schemeDetail="schemeDetail"
              :readonly="true"
              @otherAttachmentsEmit="handleOtherAttachmentsEmit"
            />
            <!-- 编辑模式：只传递 otherAttachmentList，确保编辑功能正常 -->
            <billUploadschemeOtherAttachments
              v-else
              ref="otherAttachmentsRef"
              :otherAttachmentList="otherAttachmentList"
              :readonly="false"
              @otherAttachmentsEmit="handleOtherAttachmentsEmit"
            />
          </div>
        </div>

        <slot name="billUpload"></slot>
      </div>

      <!-- 关联账单弹框 -->
      <RelatedBillDialog
        v-model:visible="relatedBillVisible"
        :bill-type="currentBillType"
        :bill-data="currentBillData"
        :demandInfo="{
          ...schemePlanObj,
          ...schemeDetail,
          ...demandDetail,
          schemeMaterial: schemeMaterialObj?.schemeMaterial,
          presents: schemePresentArr,
          others: schemeOtherArr,
          serviceFee: schemeFeeObj?.serviceFee,
          additionalItems: additionalItems,
        }"
        :existing-related-bills="currentBillData?.relatedBills || []"
        :excluded-bill-ids="getAllRelatedBillIds(currentBillData?.tempId)"
        @confirm="handleRelatedBillConfirm"
        @updateStaysInvoiceId="handleUpdateStaysInvoiceId"
        @updateRelatedAmount="handleUpdateRelatedAmount"
      />
    </a-spin>
  </div>
</template>

<style>
@import './schemeComponent/billUploadschemeInteract.scss';
</style>
<style scoped lang="less">
.scheme_interact {
  height: 100%;
  overflow-y: auto;

  .demand_reject_reason {
    padding: 24px;
    border-radius: 12px;
  }

  .interact_header {
  }

  .interact_content {
  }

  .interact_demand_title {
    display: flex;
    justify-content: space-between;
    background: #f5f5f5;

    .plan_title {
      display: flex;
      justify-content: center;
      align-items: center;

      width: calc(50% - 6px);
      height: 32px;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #1d2129;
      background: #d5e6ff;
      border-radius: 4px;
      border: 1px solid rgba(24, 104, 219, 0.3);
    }
  }

  .interact_hotel {
  }

  .interact_schedule_plan {
  }

  .interact_wu {
  }

  .interact_gift {
  }

  .interact_other {
  }

  .interact_service_fee {
  }

  .interact_total_table {
  }

  // 账单附件相关样式
  .interact_bill_attachment_title {
    .interact_title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;

      .interact_shu {
        width: 4px;
        height: 20px;
        background: #1868db;
        border-radius: 2px;
      }

      span {
        font-size: 18px;
        font-weight: 500;
        color: #1d2129;
      }
    }
  }

  .interact_hotel_contract {
  }

  .interact_invoice {
  }

  .interact_water_bill {
  }

  .interact_accommodation_detail {
  }

  .interact_conference_photos {
  }

  .interact_other_attachments {
  }

  .interact_insurance_attachment {
  }

  .btns_mar {
    height: 16px;
    background: #f5f5f5;
  }
  .total_checkin_info {
    .checkin_content {
      height: 40px;
      padding: 0 24px;
      background: #f7f8fa;
      border: 1px solid #e5e6eb;
      border-radius: 4px;
      display: flex;
      align-items: center;

      .checkin_label {
        font-size: 14px;
        color: #4e5969;
        margin-right: 8px;
      }

      .checkin_number {
        font-size: 14px;
        font-weight: 500;
        color: #1868db;
        margin-right: 12px;
      }

      .view_btn {
        padding: 0;
        height: auto;
        font-size: 14px;
        color: #1868db;

        &:hover {
          color: #0e4ba1;
        }
      }
    }
  }

  .interact_btns {
    width: 100%;

    height: 56px !important;
    line-height: 56px;
    padding: 0 24px;
    background: #ffffff;
    box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
    filter: blur(0px);
    border-top: 1px solid #e8e8e8;

    .flex_between {
      display: flex;
      justify-content: right;
      align-items: center;

      .sub_btns {
      }
    }
  }
}
.custom-tabs {
  margin-top: 16px;
  padding: 0 24px;
  // background: #fff;

  .tab-container {
    display: flex;
    gap: 8px;
    padding: 16px 0;
  }

  .tab-item {
    width: 148px;
    height: 42px;
    line-height: 42px;
    position: relative;
    //
    text-align: center;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;

    span {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
    }

    &:hover {
      background: #d5e6ff;
      border-color: #1868db;

      span {
        color: #1d2129;
      }
    }

    &.active {
      background: #1868db;
      border-color: #1868db;
      span {
        color: #fff;
      }
      .tab-pointer {
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 15px solid transparent;
        border-right: 15px solid transparent;
        border-top: 8px solid #1868db;
      }
    }
  }
}
</style>

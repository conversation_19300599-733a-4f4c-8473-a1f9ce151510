import { loadDataFromLocal, removeStorageItem, saveDataToLocal } from "./storageUtil";

import { HeaderConstant, ILoginUserInfo } from '@haierbusiness-front/common-libs';
export const isIhaier2App = () => {
    return window.navigator.userAgent.indexOf("Feishu") != -1;
}

/**
 * 截取url中token
 */
export const interceptUrlToken = () => {
    let urlSearchParams = new URLSearchParams(window.location.search);

    // 判断是hash模式还是history模式
    if (window.location.hash.includes('?')) {
        const searchPart = window.location.hash.split('?')[1];
        urlSearchParams = new URLSearchParams(searchPart);
    } else {
        urlSearchParams = new URLSearchParams(window.location.search);
    }

    const hbToken = urlSearchParams.get(HeaderConstant.TOKEN_KEY.key);
    if (hbToken) {
        removeStorageItem(HeaderConstant.TOKEN_KEY.key, false)
        saveDataToLocal(HeaderConstant.TOKEN_KEY.key, hbToken, false, 1000 * 60 * 60 * 24)
        const urlObject = new URL(window.location.href)
        urlObject.searchParams.delete(HeaderConstant.TOKEN_KEY.key)
        const newUrl = urlObject.origin + urlObject.pathname + urlObject.search + urlObject.hash
        history.replaceState({}, document.title, newUrl);
    }
}

/**
 * 解析token 为用户
 */
export const resolveToken = (hbToken?: string | null): ILoginUserInfo | null => {
    const jwtToken = hbToken ? hbToken : loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false);
    if (!jwtToken) {
        return null
    }
    const payloadJwt = jwtToken.split("."); //截取token，获取载体
    const payload = JSON.parse(decodeURIComponent(escape(window.atob(payloadJwt[1].replace(/-/g, "+").replace(/_/g, "/"))))); //解析，需要吧‘_’,'-'进行转换否则会无法解析
    var userinfo = JSON.parse(payload.sub) as ILoginUserInfo
    saveDataToLocal('username', userinfo.username, true)
    return userinfo;
}
export class IPageRequest {
  pageNum?: number;
  pageSize?: number;
}

export class IPageResponse<T> {
  pageNum?: number;
  pageSize?: number;
  total?: number;
  records?: T[];
}

export interface Result<T = any> {
  code: string;
  message: string;
  success: boolean;
  data?: T;
}
//参会人接口返回格式
export interface attendeeResponse<IPageResponse> {
  approvePassCount:number;
  approveRejectCount:number;
  approveWaitCount:number;
  list: IPageResponse;
}

export interface MiceResult<T> {
  code: string;
  message: string;
  success: boolean;
  data?: T;
}

export interface Editable<R> {
  save(request: R): Promise<Result>;

  edit(request: R): Promise<Result>;
}

export interface Deleteable {
  remove(id: number): Promise<Result>;
}

type keys = 'WAIT_QUOTATION' | 'WAIT_COMPARISON' | 'COMPARISON_FAILED' | 'COMPARISON_PASSED' | 'WAIT_APPROVAL' | 'IN_APPROVAL' | 'APPROVAL_REJECTED' | 'APPROVAL_COMPLETED' | 'EXPIRED' | 'COMPLETED' | 'CANCELLED';

/**
 * 询价状态常量
 */
export const PriceInQuiryStatusConstant = {
  WAIT_QUOTATION: { "type": 10, "name": "待报价" },
  WAIT_COMPARISON: { "type": 20, "name": "待确定" },
  COMPARISON_FAILED: { "type": 30, "name": "不通过" },
  COMPARISON_PASSED: { "type": 40, "name": "比价通过" },
  WAIT_APPROVAL: { "type": 50, "name": "待审批" },
  IN_APPROVAL: { "type": 60, "name": "审批中" },
  APPROVAL_REJECTED: { "type": 70, "name": "审批驳回" },
  APPROVAL_COMPLETED: { "type": 80, "name": "审批完成" },
  EXPIRED: { "type": 90, "name": "已过期" },
  COMPLETED: { "type": 100, "name": "已完成" },
  CANCELLED: { "type": 110, "name": "取消询价" },
  WAIT_PAYMENT: { "type": 120, "name": "审批撤销" },
  INVALID: { "type": 130, "name": "已失效" }
} as const;

/**
 * 根据类型获取状态信息
 */
export const getPriceInquiryStatus = (type?: number): { "type": number, "name": string } | null => {
  for (const key in PriceInQuiryStatusConstant) {
    const item = PriceInQuiryStatusConstant[key as keys];
    if (type === item.type) {
      return item;
    }
  }
  return null;
} 
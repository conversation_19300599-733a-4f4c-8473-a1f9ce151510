
export interface IBillDetail {
    /*方案提报计划截止时间 */
    submitDeadline: string;

    /*方案提报实际截止时间 */
    submitActualEndDate: string;

    /*竞价计划截止时间 */
    biddingDeadline: string;

    /*竞价实际截止时间 */
    biddingActualEndDate: string;

    /*修改方案提报截止时间证明材料附件, 当修改方案提报截止时间时必传 */
    schemeAttachment: string[];

    /*修改竞价截止时间证明材料附件, 当修改竞价截止时间时必传 */
    deadlineAttachment: string[];

    /*修改推送模式见证性资料附件,当修改推送模式时必传 */
    pushStrategyAttachment: string[];

    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*需求主表id */
    miceDemandId: number;

    /*需求发布表id */
    miceDemandPushId: number;

    /*需求发布服务商表id */
    merchantId: number;

    /*会议名称 */
    miceName: string;

    /*会议类型 发布会等 */
    miceType: number;

    /*会议举行城市,逗号分隔 */
    cityIds: string;

    /*会议举行城市名称,逗号分隔 */
    cityNames: string;

    /*需求开始时间 */
    startDate: string;

    /*需求结束时间 */
    endDate: string;

    /*总人数 */
    personTotal: number;

    /*资源池id */
    pdmMerchantPoolId: number;

    /*资源池名称 */
    pdmMerchantPoolName: string;

    /*资源池可承接项目bitmap */
    pdmMerchantPoolItems: number;

    /*资源池组id */
    pdmMerchantPoolGroupIds: string;

    /*资源池组名称 */
    pdmMerchantPoolGroupNames: string;

    /*服务商code(展示用不关联) */
    merchantCode: string;

    /*商户类型 */
    merchantType: number;

    /*商户名称 */
    merchantName: string;

    /*服务商评分,取发布时评分并固化 */
    merchantScore: number;

    /*商户合同号 */
    merchantContract: string;

    /*服务商平台使用费率 */
    merchantPlatformUsageFeeRate: number;

    /*需求方案表id */
    miceSchemeId: number;

    /*询价单主表id */
    msMarketPriceInquiryId: number;

    /*方案总金额(后端计算与前端传入比对) */
    schemeTotalPrice: number;

    /*协议总金额(后端计算与前端传入比对) */
    agreementTotalPrice: number;

    /*市场价总金额(后端计算与前端传入比对) */
    marketTotalPrice: number;

    /*账单总金额 */
    billTotalPrice: number;

    /*方案备注 */
    remarks: string;

    /*账单状态 */
    billState: number;

    /*上一版本id */
    sourceId: number;

    /*酒店信息 */
    hotels: Array<IBillDetailHotel>;

    /*账单住宿信息 */
    stays: Array<IBillDetailStay>;
  
      /*账单用餐信息 */
      caterings: Array<IBillDetailCatering>;
  
      /*账单会场信息 */
      places: Array<IBillDetailPlace>;
  
      /*账单用车信息 */
      vehicles: Array<IBillDetailVehicle>;
  
      /*账单服务人员信息 */
      attendants: Array<IBillDetailAttendant>;
  
      /*账单拓展活动信息 */
      activities: Array<IBillDetailActivity>;
  
      /*账单保险信息 */
      insurances: Array<IBillDetailInsurance>;
  
      /*账单礼品信息 */
      presents: Array<IBillDetailPresent>;
  
      /* */
      material: IBillDetailMaterial;
  
      /* */
      traffic: IBillDetailTraffic;
  
      /*账单其它信息 */
      others: Array<IBillDetailOther>;
  
      /* */
      serviceFee: IBillDetailServiceFee;
  
      /*账单补充条目信息 */
      additionalItems: Array<IBillDetailAdditionalItem>;
  
      /*账单附件一手合同信息 */
      attachmentContracts: Array<IBillDetailAttachmentContract>;
  
      /*账单附件发票信息 */
      attachmentInvoices: Array<IBillDetailAttachmentInvoice>;
  
      /*账单附件水单信息 */
      attachmentStatements: Array<IBillDetailAttachmentStatement>;
  
      /*账单附件住宿详单信息 */
      attachmentStayChecks: Array<IBillDetailAttachmentStayCheck>;
  
      /*账单会议现场照片信息 */
      attachmentPhotos: Array<IBillDetailAttachmentPhoto>;
  
      /*账单附件其他信息 */
      attachmentOthers: Array<IBillDetailAttachmentOther>;
  
      /*结算单信息 */
      balances: Array<IBillDetailBalance>;
}

export interface IBillDetailHotel {
    /*方案酒店id */
    id: number;

    /*需求发布酒店名称 */
    hotelName: string;
}

export interface IBillDetailStay {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*需求酒店id */
    miceDemandHotelId: number;

    /*需求住宿id */
    miceDemandStayId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*方案酒店id */
    miceSchemeHotelId: number;

    /*方案住宿id */
    miceSchemeStayId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*需求日期 */
    demandDate: string;

    /*房型 大床/双床/套房 */
    roomType: number;

    /*早餐类型 无早/单早/双早 */
    breakfastType: number;

    /*人数 */
    personNum: number;

    /*方案入住房间数 */
    schemeRoomNum: number;

    /*账单入住房间数 */
    billRoomNum: number;

    /*人数与房间数不一致原因 */
    discrepancyReason: string;

    /*方案单价 */
    schemeUnitPrice: number;

    /*账单单价 */
    billUnitPrice: number;

    /*协议产品id */
    agreementProductId: number;

    /*协议单价, 来源于协议产品关联协议价格 */
    agreementUnitPrice: number;

    /*市场单价, 来源于市场价比价功能 */
    marketUnitPrice: number;

    /*门市单价, 来源于协议产品关联门市价格 */
    retailUnitPrice: number;

    /*市场价询价单记录id */
    msMarketPriceInquiryDetailsId: number;

    /*方案说明 */
    description: string;

    /*上一版本id */
    sourceId: number;
}

export interface IBillDetailCatering {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*是否酒店提供用餐 */
    isInsideHotel: boolean;

    /*需求酒店id */
    miceDemandHotelId: number;

    /*需求用餐id */
    miceDemandCateringId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*方案酒店id */
    miceSchemeHotelId: number;

    /*方案用餐id */
    miceSchemeCateringId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*需求日期 */
    demandDate: string;

    /*用餐类型 */
    cateringType: number;

    /*用餐时间 午餐/晚餐 */
    cateringTime: number;

    /*方案人数 */
    schemePersonNum: number;

    /*账单人数 */
    billPersonNum: number;

    /*用餐标准 */
    demandUnitPrice: number;

    /*是否包含酒水 */
    isIncludeDrinks: boolean;

    /*方案单价 */
    schemeUnitPrice: number;

    /*账单单价 */
    billUnitPrice: number;

    /*方案说明 */
    description: string;

    /*上一版本id */
    sourceId: number;
}

export interface IBillDetailPlace {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*需求酒店id */
    miceDemandHotelId: number;

    /*需求会场id */
    miceDemandPlaceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*方案酒店id */
    miceSchemeHotelId: number;

    /*方案会场id */
    miceSchemePlaceId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*需求日期 */
    demandDate: string;

    /*会场数量 */
    placeNum: number;

    /*使用时间 上午/下午/晚间 bitmap */
    usageTime: number;

    /*使用用途 会议举行/布展搭建/会议撤场 bitmap */
    usagePurpose: number;

    /*方案会议厅 */
    schemeGuildhall: string;

    /*账单会议厅 */
    billGuildhall: string;

    /*方案人数 */
    schemePersonNum: number;

    /*账单人数 */
    billPersonNum: number;

    /*面积 */
    area: number;

    /*灯下层高 */
    underLightFloor: number;

    /*摆台形式 */
    tableType: number;

    /*是否需要led */
    hasLed: boolean;

    /*方案led数量 */
    schemeLedNum: number;

    /*账单led数量 */
    billLedNum: number;

    /*方案led来源 */
    schemeLedSource: string;

    /*账单led来源 */
    billLedSource: string;

    /*led规格说明 */
    ledSpecs: string;

    /*是否需要茶歇 */
    hasTea: boolean;

    /*茶歇标准/每人 */
    teaEachTotalPrice: number;

    /*茶歇说明 */
    teaDesc: string;

    /*方案报价会场单价 */
    schemeUnitPlacePrice: number;

    /*账单报价会场单价 */
    billUnitPlacePrice: number;

    /*方案报价led单价 */
    schemeUnitLedPrice: number;

    /*账单报价led单价 */
    billUnitLedPrice: number;

    /*方案报价茶歇单价 */
    schemeUnitTeaPrice: number;

    /*账单报价茶歇单价 */
    billUnitTeaPrice: number;

    /*市场价询价单记录id */
    msMarketPriceInquiryDetailsId: number;

    /*市场会场单价 */
    marketPriceUnitPrice: number;

    /*协议产品id */
    agreementProductId: number;

    /*协议产品单价 */
    agreementUnitPrice: number;

    /*门市单价, 来源于协议产品关联门市价格 */
    retailUnitPrice: number;

    /*方案说明 */
    description: string;

    /*上一版本id */
    sourceId: number;
}

export interface IBillDetailVehicle {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*需求用车id */
    miceDemandVehicleId: number;

    /*方案用车id */
    miceSchemeVehicleId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*需求日期 */
    demandDate: string;

    /*使用方式 单趟/包车 */
    usageType: number;

    /*使用时长 半天/全天 */
    usageTime: number;

    /*座位数 */
    seats: number;

    /*方案车辆数量 */
    schemeVehicleNum: number;

    /*账单车辆数量 */
    billVehicleNum: number;

    /*品牌 */
    brand: string;

    /*路线,多程逗号分隔 */
    route: string;

    /*方案单价 */
    schemeUnitPrice: number;

    /*账单单价 */
    billUnitPrice: number;

    /*方案说明 */
    description: string;

    /*上一版本id */
    sourceId: number;
}

export interface IBillDetailAttendant {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*需求服务人员id */
    miceDemandAttendantId: number;

    /*方案服务人员id */
    miceSchemeAttendantId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*需求日期 */
    demandDate: string;

    /*人员类型 */
    type: number;

    /*方案人数 */
    schemePersonNum: number;

    /*账单人数 */
    billPersonNum: number;

    /*工作范围 */
    duty: string;

    /*方案单价 */
    schemeUnitPrice: number;

    /*账单单价 */
    billUnitPrice: number;

    /*方案说明 */
    description: string;

    /*上一版本id */
    sourceId: number;
}

export interface IBillDetailActivity {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*需求拓展活动id */
    miceDemandActivityId: number;

    /*方案拓展活动id */
    miceSchemeActivityId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*需求日期 */
    demandDate: string;

    /*费用标准 */
    demandUnitPrice: number;

    /*方案人数 */
    schemePersonNum: number;

    /*账单人数 */
    billPersonNum: number;

    /*方案单价 */
    schemeUnitPrice: number;

    /*账单单价 */
    billUnitPrice: number;

    /*拓展活动附件 */
    paths: string[];

    /*方案说明 */
    description: string;
}

export interface IBillDetailInsurance {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*需求保险id */
    miceDemandInsuranceId: number;

    /*方案保险id */
    miceSchemeInsuranceId: number;

    /*账单主表id */
    miceBillId: number;

    /*需求日期 */
    demandDate: string;

    /*需求单价 */
    demandUnitPrice: number;

    /*方案参保人数 */
    schemePersonNum: number;

    /*账单参保人数 */
    billPersonNum: number;

    /*保险产品id(以互动时为准,需求时只为意向) */
    productId: number;

    /*产品所属商户id */
    productMerchantId: number;

    /*险种名称 */
    insuranceName: string;

    /*险种条目 */
    insuranceContent: string;

    /*方案单价 */
    schemeUnitPrice: number;

    /*账单单价 */
    billUnitPrice: number;

    /*方案说明 */
    description: string;

    /*上一版本id */
    sourceId: number;

    /*保单附件 */
    insuranceAttachments: string[];
}

export interface IBillDetailPresentDetail {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*方案礼品id */
    miceSchemePresentId: number;

    /*需求礼品id */
    miceDemandPresentId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单礼品id */
    miceBillPresentId: number;

    /*方案单价 */
    schemeUnitPrice: number;

    /*账单单价 */
    billUnitPrice: number;

    /*送达日期 */
    deliveryDate: string;

    /*方案礼品数量 */
    schemePersonNum: number;

    /*账单礼品数量 */
    billPersonNum: number;

    /*礼品产品id(以互动时为准,需求时只为意向) */
    productId: number;

    /*产品所属商户id */
    productMerchantId: number;

    /*产品名称,当未选择产品时可自由修改 */
    productName: string;

    /*选择方式 0:手动填写,1:选择产品 */
    optionType: number;

    /*单位 */
    unit: string;

    /*礼品说明 */
    personSpecs: string;

    /*上一版本id */
    sourceId: number;
}

export interface IBillDetailPresent {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*需求礼品id */
    miceDemandPresentId: number;

    /*方案礼品id */
    miceSchemePresentId: number;

    /*账单主表id */
    miceBillId: number;

    /*费用标准 */
    demandTotalPrice: number;

    /*方案总金额 */
    schemeTotalPrice: number;

    /*账单总金额 */
    billTotalPrice: number;

    /*方案说明 */
    description: string;

    /*上一版本id */
    sourceId: number;

    /*账单礼品信息 */
    presentDetails: Array<IBillDetailPresentDetail>;
}

export interface IBillDetailMaterialDetail {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*方案布展物料id */
    miceSchemeMaterialId: number;

    /*需求布展物料表明细id */
    miceDemandMaterialDetailsId: number;

    /*方案布展物料表明细id */
    miceSchemeMaterialDetailsId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单布展物料表id */
    miceBillMaterialId: number;

    /*物料类型 枚举 */
    type: number;

    /*规格说明 */
    specs: string;

    /*方案物料数量 */
    schemeMaterialNum: number;

    /*账单物料数量 */
    billMaterialNum: number;

    /*需求单价 */
    demandUnitPrice: number;

    /*方案报价单价 */
    schemeUnitPrice: number;

    /*账单报价单价 */
    billUnitPrice: number;

    /*上一版本id */
    sourceId: number;
}

export interface IBillDetailMaterial {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*需求布展物料id */
    miceDemandMaterialId: number;

    /*方案布展物料id */
    miceSchemeMaterialId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*费用标准/总 */
    demandTotalPrice: number;

    /*方案总价 */
    schemeTotalPrice: number;

    /*账单总价 */
    billTotalPrice: number;

    /*方案说明 */
    description: string;

    /*上一版本id */
    sourceId: number;

    /*账单物料明细信息 */
    materialDetails: Array<IBillDetailMaterialDetail>;
}

export interface IBillDetailTraffic {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*需求交通id */
    miceDemandTrafficId: number;

    /*方案交通id */
    miceSchemeTrafficId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*需求总金额 */
    demandTotalPrice: number;

    /*方案总金额 */
    schemeTotalPrice: number;

    /*账单总金额 */
    billTotalPrice: number;

    /*交通主表id */
    miceSchemeTrafficMainId: number;

    /*上一版本id */
    sourceId: number;
}

export interface IBillDetailOther {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*需求其它id */
    miceDemandOtherId: number;

    /*方案其它id */
    miceSchemeOtherId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*需求日期 */
    demandDate: string;

    /*费用标准 */
    demandTotalPrice: number;

    /*项目 */
    itemName: string;

    /*方案数量(不参与总价计算) */
    num: number;

    /*账单数量(不参与总价计算) */
    billNum: number;

    /*单位 */
    unit: string;

    /*规格描述 */
    specs: string;

    /*方案总金额 */
    schemeTotalPrice: number;

    /*账单总金额 */
    billTotalPrice: number;

    /*方案说明 */
    description: string;

    /*上一版本id */
    sourceId: number;
}

export interface IBillDetailServiceFee {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*方案主表id */
    miceSchemeId: number;

    /*方案服务费id */
    miceSchemeServiceFeeId: number;

    /*账单主表id */
    miceBillId: number;

    /*账单发票id */
    miceBillAttachmentInvoiceId: number;

    /*账单水单id */
    miceBillAttachmentStatementId: number;

    /*服务商比例 */
    serviceFeeRate: number;

    /*服务商比例上限 */
    serviceFeeLimitRate: number;

    /*服务商对应上限金额 */
    serviceFeeLimit: number;

    /*方案服务费金额 */
    schemeServiceFeeReal: number;

    /*账单服务费金额 */
    billServiceFeeReal: number;
}

export interface IBillDetailAdditionalItem {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*账单主表id */
    miceBillId: number;

    /*临时id, 用于关联发票表 */
    invoiceTempId: number;

    /*临时id, 用于关联水单表 */
    statementTempId: number;

    /*发生时间 */
    occurDate: string;

    /*项目名称 */
    itemName: string;

    /*类型 客损/其它等 */
    type: number;

    /*账单数量 */
    billNum: number;

    /*账单单价 */
    billUnitPrice: number;

    /*描述 */
    description: string;

    /*补充条目附件 */
    attachments: string[];
}

export interface IBillDetailAttachmentContract {
    /*子类型,传酒店名称/类型是会前会中附件说明之类的 */
    subType: string;

    /*附件路径 */
    paths: string[];
}

export interface IBillDetailAttachmentInvoice {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*账单主表id */
    miceBillId: number;

    /*发生时间 */
    occurDate: string;

    /*当地货币 */
    localCurrency: number;

    /*单位 */
    unit: string;

    /*汇率 */
    exchangeRate: number;

    /*总金额(人民币) */
    totalAmountCny: number;

    /*关联金额合计（人民币） */
    relatedAmountTotalCny: number;

    /*发票附件路径 */
    paths: string[];

    /*汇率见证资料 */
    path: string;
}

export interface IBillDetailAttachmentStatement {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*账单主表id */
    miceBillId: number;

    /*发生时间 */
    occurDate: string;

    /*当地货币 */
    localCurrency: number;

    /*单位 */
    unit: string;

    /*汇率 */
    exchangeRate: number;

    /*总金额(人民币) */
    totalAmountCny: number;

    /*关联金额合计（人民币） */
    relatedAmountTotalCny: number;

    /*水单附件路径 */
    paths: string[];

    /*汇率见证性材料 */
    path: string;
}

export interface IBillDetailAttachmentStayCheck {
    /*主键 */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*账单主表id */
    miceBillId: number;

    /*发生时间 */
    occurDate: string;

    /*签到人数 */
    checkInPersonNum: number;

    /*详单人数 */
    detailPersonNum: number;

    /*比对结果(0:不一致,1:一致) */
    comparisonResult: boolean;

    /*附件路径 */
    paths: string[];
}

export interface IBillDetailAttachmentPhoto {
    /*子类型,传酒店名称/类型是会前会中附件说明之类的 */
    subType: string;

    /*附件路径 */
    paths: string[];
}

export interface IBillDetailAttachmentOther {
    /*子类型,传酒店名称/类型是会前会中附件说明之类的 */
    subType: string;

    /*附件路径 */
    paths: string[];
}

export interface IBillDetailBalance {
    /*子类型,传项目名称 */
    subType: string;

    /*结算单附件 */
    balanceAttachment: string[];
}